
/*-----------------------Custom Fonts-------------------------*/

/* Arabic Fonts from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap');
@font-face {
  font-family: 'Josef<PERSON> Sans';
  font-style: normal;
  font-weight: 100;
  src: url(../fonts/JosefinSans-Thin.eot);
  src: url(../fonts/JosefinSans-Thin.woff) format('woff'),
       url(../fonts/JosefinSans-Thin.woff2) format('woff2');
}
@font-face {
  font-family: '<PERSON><PERSON> San<PERSON>';
  font-style: normal;
  font-weight: 300;
  src: url(../fonts/JosefinSans-Light.eot);
  src: url(../fonts/JosefinSans-Light.woff) format('woff'),
       url(../fonts/JosefinSans-Light.woff2) format('woff2');
}

@font-face {
  font-family: 'Josefin Sans';
  font-style: normal;
  font-weight: 400;
  src: url(../fonts/JosefinSans-Regular.eot);
  src: url(../fonts/JosefinSans-Regular.woff) format('woff'),
       url(../fonts/JosefinSans-Regular.woff2) format('woff2');
}
@font-face {
  font-family: 'Josefin Sans';
  font-style: normal;
  font-weight: 600;
  src: url(../fonts/JosefinSans-SemiBold.eot);
  src: url(../fonts/JosefinSans-SemiBold.woff) format('woff'),
       url(../fonts/JosefinSans-SemiBold.woff2) format('woff2');
}
@font-face {
  font-family: 'Josefin Sans';
  font-style: normal;
  font-weight: 700;
  src: url(../fonts/JosefinSans-Bold.eot);
  src: url(../fonts/JosefinSans-Bold.woff) format('woff'),
       url(../fonts/JosefinSans-Bold.woff2) format('woff2');
}