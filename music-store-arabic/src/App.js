import React, { useState } from 'react';
import './App.css';
import MusicList from './components/MusicList';

function App() {
  const [showMusicList, setShowMusicList] = useState(false);

  const toggleMusicList = () => {
    setShowMusicList(!showMusicList);
  };

  if (showMusicList) {
    return (
      <div className="App" dir="rtl">
        <div style={{
          background: 'linear-gradient(135deg, #14182a 0%, #1b2039 100%)',
          minHeight: '100vh',
          padding: '20px'
        }}>
          <div style={{
            textAlign: 'center',
            marginBottom: '20px',
            padding: '20px'
          }}>
            <button
              onClick={toggleMusicList}
              style={{
                background: 'linear-gradient(45deg, #3bc8e7, #2ba8c7)',
                color: 'white',
                border: 'none',
                borderRadius: '25px',
                padding: '12px 24px',
                fontSize: '16px',
                fontWeight: 'bold',
                cursor: 'pointer',
                fontFamily: 'Cairo, Arial, sans-serif',
                marginBottom: '20px'
              }}
            >
              ← العودة للصفحة الرئيسية
            </button>
          </div>
          <MusicList />
        </div>
      </div>
    );
  }

  return (
    <div className="App" dir="rtl">
      <header className="App-header">
        <h1 style={{ fontFamily: 'Cairo, Arial, sans-serif', color: '#3bc8e7' }}>
          🎵 مرحباً بك في متجر الموسيقى العربي
        </h1>
        <p style={{ fontSize: '18px', marginBottom: '20px' }}>
          متجر الموسيقى الإلكتروني - استمع لأفضل الأغاني والألبومات
        </p>
        <div style={{ display: 'flex', gap: '20px', flexDirection: 'column', alignItems: 'center' }}>
          <div style={{
            background: 'linear-gradient(45deg, #3bc8e7, #2ba8c7)',
            padding: '15px 30px',
            borderRadius: '8px',
            color: 'white',
            fontWeight: 'bold'
          }}>
            ✅ React App يعمل بنجاح!
          </div>

          <button
            onClick={toggleMusicList}
            style={{
              background: 'linear-gradient(45deg, #61dafb, #3bc8e7)',
              color: 'white',
              border: 'none',
              borderRadius: '25px',
              padding: '15px 30px',
              fontSize: '18px',
              fontWeight: 'bold',
              cursor: 'pointer',
              fontFamily: 'Cairo, Arial, sans-serif',
              transition: 'transform 0.2s ease',
              boxShadow: '0 4px 15px rgba(59, 200, 231, 0.3)'
            }}
            onMouseEnter={(e) => e.target.style.transform = 'scale(1.05)'}
            onMouseLeave={(e) => e.target.style.transform = 'scale(1)'}
          >
            🎶 استكشف مكتبة الموسيقى
          </button>

          <div style={{
            background: '#1b2039',
            padding: '20px',
            borderRadius: '10px',
            maxWidth: '600px',
            textAlign: 'right'
          }}>
            <h3 style={{ color: '#3bc8e7', marginBottom: '15px' }}>الميزات المتوفرة:</h3>
            <ul style={{ listStyle: 'none', padding: 0 }}>
              <li style={{ marginBottom: '10px' }}>🎶 مشغل موسيقى متقدم</li>
              <li style={{ marginBottom: '10px' }}>📱 تصميم متجاوب</li>
              <li style={{ marginBottom: '10px' }}>🔍 نظام بحث ذكي</li>
              <li style={{ marginBottom: '10px' }}>❤️ قائمة المفضلة</li>
              <li style={{ marginBottom: '10px' }}>📊 إحصائيات التشغيل</li>
              <li style={{ marginBottom: '10px' }}>🎵 قوائم التشغيل</li>
            </ul>
          </div>
          <p style={{ fontSize: '14px', color: '#777' }}>
            المشروع جاهز للتطوير والتخصيص مع WordPress
          </p>
        </div>
      </header>
    </div>
  );
}

export default App;
