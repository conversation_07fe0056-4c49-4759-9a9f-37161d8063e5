/* استيراد الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

.App {
  text-align: center;
  font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
  direction: rtl;
}

.App-header {
  background: linear-gradient(135deg, #14182a 0%, #1b2039 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
  padding: 40px 20px;
}

.App-link {
  color: #3bc8e7;
  text-decoration: none;
  transition: color 0.3s ease;
}

.App-link:hover {
  color: #61dafb;
}

/* تحسينات للنصوص العربية */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Cairo', '<PERSON>jawal', Arial, sans-serif;
  font-weight: 600;
  line-height: 1.4;
}

p, span, div {
  font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
  line-height: 1.6;
}

/* تحسينات للتصميم المتجاوب */
@media (max-width: 768px) {
  .App-header {
    padding: 20px 10px;
    font-size: calc(8px + 2vmin);
  }

  h1 {
    font-size: 24px;
  }

  p {
    font-size: 16px;
  }
}

/* تأثيرات بصرية */
.gradient-text {
  background: linear-gradient(45deg, #3bc8e7, #61dafb);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  margin: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(59, 200, 231, 0.3);
}

/* أنيميشن للعناصر */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* تحسينات لشريط التمرير */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1b2039;
}

::-webkit-scrollbar-thumb {
  background: #3bc8e7;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #61dafb;
}
