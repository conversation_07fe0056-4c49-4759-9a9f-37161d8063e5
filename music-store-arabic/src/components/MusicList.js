import React, { useState } from 'react';
import MusicCard from './MusicCard';
import Header from './Header';

const MusicList = () => {
  const [currentPlaying, setCurrentPlaying] = useState(null);

  // بيانات تجريبية للأغاني
  const songs = [
    {
      id: 1,
      title: 'احلم بلحظاتك',
      artist: 'آفا كورنيش وبرايان هيل',
      image: 'https://via.placeholder.com/300x300/3bc8e7/ffffff?text=أغنية+1',
      duration: '3:45'
    },
    {
      id: 2,
      title: 'حتى التقيت بك',
      artist: 'آفا كورنيش وبرايان هيل',
      image: 'https://via.placeholder.com/300x300/2ba8c7/ffffff?text=أغنية+2',
      duration: '4:12'
    },
    {
      id: 3,
      title: 'امنحني بعض الشجاعة',
      artist: 'آفا كورنيش وبرايان هيل',
      image: 'https://via.placeholder.com/300x300/1b2039/ffffff?text=أغنية+3',
      duration: '3:28'
    },
    {
      id: 4,
      title: 'الزقاق المظلم الصوتي',
      artist: 'آفا كورنيش وبرايان هيل',
      image: 'https://via.placeholder.com/300x300/14182a/ffffff?text=أغنية+4',
      duration: '4:05'
    },
    {
      id: 5,
      title: 'وعود المشي',
      artist: 'آفا كورنيش وبرايان هيل',
      image: 'https://via.placeholder.com/300x300/61dafb/ffffff?text=أغنية+5',
      duration: '3:52'
    },
    {
      id: 6,
      title: 'الألعاب المرغوبة',
      artist: 'آفا كورنيش وبرايان هيل',
      image: 'https://via.placeholder.com/300x300/ff6b6b/ffffff?text=أغنية+6',
      duration: '4:18'
    }
  ];

  const containerStyle = {
    padding: '20px',
    maxWidth: '1200px',
    margin: '0 auto',
    direction: 'rtl'
  };

  const titleStyle = {
    color: '#3bc8e7',
    fontSize: '32px',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: '30px',
    fontFamily: 'Cairo, Arial, sans-serif'
  };

  const gridStyle = {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
    gap: '20px',
    marginBottom: '30px'
  };

  const currentPlayingStyle = {
    background: 'rgba(59, 200, 231, 0.1)',
    border: '2px solid #3bc8e7',
    borderRadius: '15px',
    padding: '20px',
    textAlign: 'center',
    marginTop: '30px'
  };

  const handlePlay = (song) => {
    setCurrentPlaying(song);
    console.log('تشغيل الأغنية:', song.title);
  };

  return (
    <div>
      <Header />
      <div style={containerStyle}>
        <h2 style={titleStyle}>🎵 مكتبة الموسيقى العربية</h2>
      
      <div style={gridStyle}>
        {songs.map(song => (
          <MusicCard
            key={song.id}
            title={song.title}
            artist={song.artist}
            image={song.image}
            duration={song.duration}
            onPlay={handlePlay}
          />
        ))}
      </div>

      {currentPlaying && (
        <div style={currentPlayingStyle}>
          <h3 style={{ color: '#3bc8e7', marginBottom: '10px' }}>
            🎶 يتم تشغيل الآن
          </h3>
          <p style={{ color: '#fff', fontSize: '18px', fontWeight: 'bold' }}>
            {currentPlaying.title}
          </p>
          <p style={{ color: '#ccc', fontSize: '14px' }}>
            {currentPlaying.artist}
          </p>
          <div style={{ 
            background: '#3bc8e7', 
            height: '4px', 
            borderRadius: '2px',
            margin: '15px 0',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <div style={{
              background: '#fff',
              height: '100%',
              width: '30%',
              borderRadius: '2px',
              animation: 'progress 3s ease-in-out infinite'
            }}></div>
          </div>
          <p style={{ color: '#777', fontSize: '12px' }}>
            المدة: {currentPlaying.duration}
          </p>
        </div>
      )}

      </div>
    </div>
  );
};

export default MusicList;
