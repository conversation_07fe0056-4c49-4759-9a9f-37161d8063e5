import React, { useState } from 'react';

const Header = () => {
  const [searchQuery, setSearchQuery] = useState('');

  const headerStyle = {
    background: 'rgba(27, 32, 57, 0.95)',
    backdropFilter: 'blur(10px)',
    padding: '15px 30px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottom: '1px solid rgba(59, 200, 231, 0.3)',
    position: 'sticky',
    top: 0,
    zIndex: 1000,
    direction: 'rtl'
  };

  const logoStyle = {
    color: '#3bc8e7',
    fontSize: '24px',
    fontWeight: 'bold',
    fontFamily: 'Cairo, Arial, sans-serif'
  };

  const searchContainerStyle = {
    display: 'flex',
    alignItems: 'center',
    background: 'rgba(255, 255, 255, 0.1)',
    borderRadius: '25px',
    padding: '8px 20px',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    minWidth: '300px'
  };

  const searchInputStyle = {
    background: 'transparent',
    border: 'none',
    outline: 'none',
    color: 'white',
    fontSize: '14px',
    fontFamily: 'Cairo, Arial, sans-serif',
    width: '100%',
    textAlign: 'right'
  };

  const navStyle = {
    display: 'flex',
    gap: '20px',
    alignItems: 'center'
  };

  const navLinkStyle = {
    color: '#ccc',
    background: 'transparent',
    border: 'none',
    fontSize: '14px',
    fontFamily: 'Cairo, Arial, sans-serif',
    transition: 'color 0.3s ease',
    cursor: 'pointer'
  };

  const buttonStyle = {
    background: 'linear-gradient(45deg, #3bc8e7, #2ba8c7)',
    color: 'white',
    border: 'none',
    borderRadius: '20px',
    padding: '8px 16px',
    fontSize: '12px',
    fontWeight: 'bold',
    cursor: 'pointer',
    fontFamily: 'Cairo, Arial, sans-serif',
    transition: 'transform 0.2s ease'
  };

  const handleSearch = (e) => {
    e.preventDefault();
    console.log('البحث عن:', searchQuery);
  };

  return (
    <header style={headerStyle}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '30px' }}>
        <div style={logoStyle}>
          🎵 معجزة
        </div>
        
        <form onSubmit={handleSearch} style={searchContainerStyle}>
          <input
            type="text"
            placeholder="ابحث عن الموسيقى هنا..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            style={searchInputStyle}
          />
          <button 
            type="submit" 
            style={{ 
              background: 'transparent', 
              border: 'none', 
              color: '#3bc8e7',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            🔍
          </button>
        </form>
      </div>

      <nav style={navStyle}>
        <button
          style={navLinkStyle}
          onMouseEnter={(e) => e.target.style.color = '#3bc8e7'}
          onMouseLeave={(e) => e.target.style.color = '#ccc'}
        >
          الرئيسية
        </button>
        <button
          style={navLinkStyle}
          onMouseEnter={(e) => e.target.style.color = '#3bc8e7'}
          onMouseLeave={(e) => e.target.style.color = '#ccc'}
        >
          الألبومات
        </button>
        <button
          style={navLinkStyle}
          onMouseEnter={(e) => e.target.style.color = '#3bc8e7'}
          onMouseLeave={(e) => e.target.style.color = '#ccc'}
        >
          الفنانين
        </button>
        <button
          style={navLinkStyle}
          onMouseEnter={(e) => e.target.style.color = '#3bc8e7'}
          onMouseLeave={(e) => e.target.style.color = '#ccc'}
        >
          المفضلة
        </button>
        
        <button 
          style={buttonStyle}
          onMouseEnter={(e) => e.target.style.transform = 'scale(1.05)'}
          onMouseLeave={(e) => e.target.style.transform = 'scale(1)'}
        >
          تسجيل الدخول
        </button>
      </nav>
    </header>
  );
};

export default Header;
