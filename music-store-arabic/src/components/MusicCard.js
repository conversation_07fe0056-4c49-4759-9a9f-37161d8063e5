import React from 'react';

const MusicCard = ({ title, artist, image, duration, onPlay }) => {
  const cardStyle = {
    background: 'rgba(255, 255, 255, 0.1)',
    backdropFilter: 'blur(10px)',
    borderRadius: '15px',
    padding: '20px',
    margin: '10px',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    transition: 'transform 0.3s ease, box-shadow 0.3s ease',
    cursor: 'pointer',
    textAlign: 'right',
    direction: 'rtl'
  };

  const imageStyle = {
    width: '100%',
    height: '200px',
    objectFit: 'cover',
    borderRadius: '10px',
    marginBottom: '15px'
  };

  const titleStyle = {
    color: '#3bc8e7',
    fontSize: '18px',
    fontWeight: 'bold',
    marginBottom: '8px',
    fontFamily: 'Cairo, Arial, sans-serif'
  };

  const artistStyle = {
    color: '#ccc',
    fontSize: '14px',
    marginBottom: '10px',
    fontFamily: 'Cairo, Arial, sans-serif'
  };

  const durationStyle = {
    color: '#777',
    fontSize: '12px',
    fontFamily: 'Cairo, Arial, sans-serif'
  };

  const playButtonStyle = {
    background: 'linear-gradient(45deg, #3bc8e7, #2ba8c7)',
    color: 'white',
    border: 'none',
    borderRadius: '25px',
    padding: '10px 20px',
    fontSize: '14px',
    fontWeight: 'bold',
    cursor: 'pointer',
    transition: 'transform 0.2s ease',
    fontFamily: 'Cairo, Arial, sans-serif',
    marginTop: '10px'
  };

  const handleMouseEnter = (e) => {
    e.target.style.transform = 'translateY(-5px)';
    e.target.style.boxShadow = '0 10px 25px rgba(59, 200, 231, 0.3)';
  };

  const handleMouseLeave = (e) => {
    e.target.style.transform = 'translateY(0)';
    e.target.style.boxShadow = 'none';
  };

  const handlePlayClick = (e) => {
    e.stopPropagation();
    if (onPlay) {
      onPlay({ title, artist, image, duration });
    }
  };

  return (
    <div 
      style={cardStyle}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <img src={image} alt={title} style={imageStyle} />
      <div style={titleStyle}>{title}</div>
      <div style={artistStyle}>{artist}</div>
      <div style={durationStyle}>المدة: {duration}</div>
      <button 
        style={playButtonStyle}
        onClick={handlePlayClick}
        onMouseEnter={(e) => e.target.style.transform = 'scale(1.05)'}
        onMouseLeave={(e) => e.target.style.transform = 'scale(1)'}
      >
        ▶️ تشغيل
      </button>
    </div>
  );
};

export default MusicCard;
