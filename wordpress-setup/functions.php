<?php
/**
 * إعداد WordPress للعمل مع React Music Store
 * يجب إضافة هذا الكود إلى ملف functions.php في الثيم
 */

// تفعيل دعم CORS للـ REST API
add_action('rest_api_init', function() {
    remove_filter('rest_pre_serve_request', 'rest_send_cors_headers');
    add_filter('rest_pre_serve_request', function($value) {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-WP-Nonce');
        header('Access-Control-Allow-Credentials: true');
        return $value;
    });
});

// إنشاء Custom Post Type للأغاني
function create_tracks_post_type() {
    register_post_type('tracks',
        array(
            'labels' => array(
                'name' => 'الأغاني',
                'singular_name' => 'أغنية',
                'add_new' => 'إضافة أغنية جديدة',
                'add_new_item' => 'إضافة أغنية جديدة',
                'edit_item' => 'تعديل الأغنية',
                'new_item' => 'أغنية جديدة',
                'view_item' => 'عرض الأغنية',
                'search_items' => 'البحث في الأغاني',
                'not_found' => 'لم يتم العثور على أغاني',
                'not_found_in_trash' => 'لم يتم العثور على أغاني في المهملات'
            ),
            'public' => true,
            'has_archive' => true,
            'show_in_rest' => true,
            'rest_base' => 'tracks',
            'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
            'menu_icon' => 'dashicons-format-audio',
            'rewrite' => array('slug' => 'tracks'),
        )
    );
}
add_action('init', 'create_tracks_post_type');

// إنشاء Custom Post Type للألبومات
function create_albums_post_type() {
    register_post_type('albums',
        array(
            'labels' => array(
                'name' => 'الألبومات',
                'singular_name' => 'ألبوم',
                'add_new' => 'إضافة ألبوم جديد',
                'add_new_item' => 'إضافة ألبوم جديد',
                'edit_item' => 'تعديل الألبوم',
                'new_item' => 'ألبوم جديد',
                'view_item' => 'عرض الألبوم',
                'search_items' => 'البحث في الألبومات',
                'not_found' => 'لم يتم العثور على ألبومات',
                'not_found_in_trash' => 'لم يتم العثور على ألبومات في المهملات'
            ),
            'public' => true,
            'has_archive' => true,
            'show_in_rest' => true,
            'rest_base' => 'albums',
            'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
            'menu_icon' => 'dashicons-album',
            'rewrite' => array('slug' => 'albums'),
        )
    );
}
add_action('init', 'create_albums_post_type');

// إنشاء Custom Post Type للفنانين
function create_artists_post_type() {
    register_post_type('artists',
        array(
            'labels' => array(
                'name' => 'الفنانين',
                'singular_name' => 'فنان',
                'add_new' => 'إضافة فنان جديد',
                'add_new_item' => 'إضافة فنان جديد',
                'edit_item' => 'تعديل الفنان',
                'new_item' => 'فنان جديد',
                'view_item' => 'عرض الفنان',
                'search_items' => 'البحث في الفنانين',
                'not_found' => 'لم يتم العثور على فنانين',
                'not_found_in_trash' => 'لم يتم العثور على فنانين في المهملات'
            ),
            'public' => true,
            'has_archive' => true,
            'show_in_rest' => true,
            'rest_base' => 'artists',
            'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
            'menu_icon' => 'dashicons-admin-users',
            'rewrite' => array('slug' => 'artists'),
        )
    );
}
add_action('init', 'create_artists_post_type');

// إنشاء Custom Taxonomy للأنواع الموسيقية
function create_genres_taxonomy() {
    register_taxonomy(
        'genres',
        array('tracks', 'albums'),
        array(
            'labels' => array(
                'name' => 'الأنواع الموسيقية',
                'singular_name' => 'نوع موسيقي',
                'search_items' => 'البحث في الأنواع',
                'all_items' => 'جميع الأنواع',
                'edit_item' => 'تعديل النوع',
                'update_item' => 'تحديث النوع',
                'add_new_item' => 'إضافة نوع جديد',
                'new_item_name' => 'اسم النوع الجديد',
                'menu_name' => 'الأنواع الموسيقية',
            ),
            'hierarchical' => true,
            'show_ui' => true,
            'show_admin_column' => true,
            'query_var' => true,
            'show_in_rest' => true,
            'rest_base' => 'genres',
            'rewrite' => array('slug' => 'genre'),
        )
    );
}
add_action('init', 'create_genres_taxonomy');

// إضافة Custom Fields للأغاني
function add_track_meta_boxes() {
    add_meta_box(
        'track_details',
        'تفاصيل الأغنية',
        'track_details_callback',
        'tracks',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_track_meta_boxes');

function track_details_callback($post) {
    wp_nonce_field('save_track_details', 'track_details_nonce');
    
    $artist = get_post_meta($post->ID, 'artist', true);
    $duration = get_post_meta($post->ID, 'duration', true);
    $audio_file = get_post_meta($post->ID, 'audio_file', true);
    $album = get_post_meta($post->ID, 'album', true);
    $release_date = get_post_meta($post->ID, 'release_date', true);
    $play_count = get_post_meta($post->ID, 'play_count', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="artist">الفنان:</label></th>';
    echo '<td><input type="text" id="artist" name="artist" value="' . esc_attr($artist) . '" style="width: 100%;" /></td></tr>';
    
    echo '<tr><th><label for="duration">المدة:</label></th>';
    echo '<td><input type="text" id="duration" name="duration" value="' . esc_attr($duration) . '" placeholder="3:45" style="width: 100%;" /></td></tr>';
    
    echo '<tr><th><label for="audio_file">ملف الصوت:</label></th>';
    echo '<td><input type="url" id="audio_file" name="audio_file" value="' . esc_attr($audio_file) . '" style="width: 100%;" /></td></tr>';
    
    echo '<tr><th><label for="album">الألبوم:</label></th>';
    echo '<td><input type="text" id="album" name="album" value="' . esc_attr($album) . '" style="width: 100%;" /></td></tr>';
    
    echo '<tr><th><label for="release_date">تاريخ الإصدار:</label></th>';
    echo '<td><input type="date" id="release_date" name="release_date" value="' . esc_attr($release_date) . '" style="width: 100%;" /></td></tr>';
    
    echo '<tr><th><label for="play_count">عدد مرات التشغيل:</label></th>';
    echo '<td><input type="number" id="play_count" name="play_count" value="' . esc_attr($play_count) . '" style="width: 100%;" /></td></tr>';
    
    echo '</table>';
}

// حفظ Custom Fields للأغاني
function save_track_details($post_id) {
    if (!isset($_POST['track_details_nonce']) || !wp_verify_nonce($_POST['track_details_nonce'], 'save_track_details')) {
        return;
    }
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    $fields = array('artist', 'duration', 'audio_file', 'album', 'release_date', 'play_count');
    
    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('save_post', 'save_track_details');

// إضافة endpoint مخصص لتسجيل التشغيل
function register_play_endpoint() {
    register_rest_route('wp/v2', '/tracks/(?P<id>\d+)/play', array(
        'methods' => 'POST',
        'callback' => 'handle_track_play',
        'permission_callback' => '__return_true',
        'args' => array(
            'id' => array(
                'validate_callback' => function($param, $request, $key) {
                    return is_numeric($param);
                }
            ),
        ),
    ));
}
add_action('rest_api_init', 'register_play_endpoint');

function handle_track_play($request) {
    $track_id = $request['id'];
    $current_count = get_post_meta($track_id, 'play_count', true);
    $new_count = intval($current_count) + 1;
    
    update_post_meta($track_id, 'play_count', $new_count);
    
    return array(
        'success' => true,
        'track_id' => $track_id,
        'play_count' => $new_count
    );
}

// إضافة دعم الصور المميزة
add_theme_support('post-thumbnails');

// إضافة أحجام صور مخصصة
add_image_size('track-thumbnail', 300, 300, true);
add_image_size('album-cover', 500, 500, true);
add_image_size('artist-photo', 400, 400, true);

// تخصيص REST API response لإضافة Custom Fields
function add_custom_fields_to_rest_api() {
    // إضافة Custom Fields للأغاني
    register_rest_field('tracks', 'acf', array(
        'get_callback' => function($post) {
            return array(
                'artist' => get_post_meta($post['id'], 'artist', true),
                'duration' => get_post_meta($post['id'], 'duration', true),
                'audio_file' => get_post_meta($post['id'], 'audio_file', true),
                'album' => get_post_meta($post['id'], 'album', true),
                'release_date' => get_post_meta($post['id'], 'release_date', true),
                'play_count' => get_post_meta($post['id'], 'play_count', true),
            );
        }
    ));
    
    // إضافة Custom Fields للألبومات
    register_rest_field('albums', 'acf', array(
        'get_callback' => function($post) {
            return array(
                'artist' => get_post_meta($post['id'], 'artist', true),
                'release_date' => get_post_meta($post['id'], 'release_date', true),
                'tracks_count' => get_post_meta($post['id'], 'tracks_count', true),
                'featured' => get_post_meta($post['id'], 'featured', true),
                'subtitle' => get_post_meta($post['id'], 'subtitle', true),
            );
        }
    ));
}
add_action('rest_api_init', 'add_custom_fields_to_rest_api');

?>
