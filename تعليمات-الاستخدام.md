# 🎵 تعليمات استخدام موقع الموسيقى العربي

## 🚀 **كيفية تشغيل الموقع**

### **الطريقة الأولى: فتح الملف مباشرة**
1. افتح ملف `index.html` في أي متصفح ويب
2. الموقع سيعمل بالعربية مع دعم RTL تلقائياً

### **الطريقة الثانية: استخدام خادم محلي**
```bash
# إذا كان لديك Python مثبت
python -m http.server 8000

# أو إذا كان لديك Node.js
npx http-server

# ثم افتح المتصفح على
http://localhost:8000
```

## 📁 **الملفات المهمة**

### **الصفحات العربية:**
- `index.html` - الصفحة الرئيسية (مترجمة بالكامل)
- `index-ar.html` - نسخة احتياطية
- `index-arabic.html` - نسخة أخرى

### **ملفات التصميم:**
- `css/style.css` - يحتوي على قواعد RTL
- `css/fonts.css` - يحتوي على الخطوط العربية

## 🎨 **الميزات المتوفرة**

### ✅ **مترجم بالكامل:**
- القائمة الجانبية
- شريط البحث
- البانر الرئيسي
- قوائم الأغاني
- أزرار التحكم
- قوائم الخيارات

### ✅ **دعم RTL كامل:**
- اتجاه النصوص من اليمين لليسار
- عكس اتجاه القوائم والأيقونات
- محاذاة صحيحة للعناصر
- خطوط عربية جميلة

### ✅ **متجاوب:**
- يعمل على الكمبيوتر
- يعمل على الهواتف
- يعمل على الأجهزة اللوحية

## 🔧 **التخصيص**

### **تغيير الخطوط:**
في ملف `css/fonts.css` يمكنك إضافة خطوط أخرى:
```css
@import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap');
```

### **تغيير الألوان:**
في ملف `css/style.css` ابحث عن:
```css
background-color: #14182a; /* لون الخلفية */
color: #3bc8e7; /* اللون الأساسي */
```

### **إضافة صفحات جديدة:**
1. انسخ `index.html`
2. غير المحتوى حسب الحاجة
3. تأكد من وجود `class="rtl"` في `<body>`

## 🎵 **أقسام الموقع**

### **القائمة الجانبية:**
- اكتشف
- الألبومات
- الفنانين
- الأنواع
- أفضل الأغاني
- موسيقى مجانية
- المحطات

### **قسم التحميلات:**
- التحميلات
- المشتريات
- المفضلة
- التاريخ

### **قوائم التشغيل:**
- قوائم مميزة
- إنشاء قائمة

### **أخرى:**
- الملف الشخصي
- رفع الملفات
- المدونة

## 🔍 **البحث**
- مربع البحث في الأعلى
- يدعم البحث بالعربية
- نتائج فورية

## 🎛️ **مشغل الموسيقى**
- تشغيل/إيقاف
- التحكم في الصوت
- قائمة التشغيل
- أزرار التحكم

## 📱 **التوافق**
- Chrome ✅
- Firefox ✅
- Safari ✅
- Edge ✅
- الهواتف الذكية ✅
- الأجهزة اللوحية ✅

## 🆘 **المساعدة**
إذا واجهت أي مشاكل:
1. تأكد من وجود جميع الملفات
2. تحقق من اتصال الإنترنت (للخطوط)
3. جرب متصفح آخر
4. تأكد من تفعيل JavaScript

---

**🎉 استمتع بموقع الموسيقى العربي الجديد!**
