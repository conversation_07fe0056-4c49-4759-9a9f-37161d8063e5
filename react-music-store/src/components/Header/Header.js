import React, { useState } from 'react';
import {
  <PERSON>er<PERSON><PERSON><PERSON>,
  HeaderLeft,
  HeaderRight,
  SearchSection,
  SearchInput,
  SearchIcon,
  TrendingSection,
  TrendingText,
  TrendingMarquee,
  LanguageSection,
  ButtonSection,
  HeaderButton
} from './HeaderStyles';

const Header = () => {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e) => {
    e.preventDefault();
    // هنا يمكن إضافة منطق البحث
    console.log('البحث عن:', searchQuery);
  };

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  return (
    <HeaderContainer>
      <HeaderLeft>
        <SearchSection>
          <form onSubmit={handleSearch}>
            <SearchInput
              type="text"
              placeholder="ابحث عن الموسيقى هنا.."
              value={searchQuery}
              onChange={handleSearchChange}
            />
            <SearchIcon onClick={handleSearch}>
              <img src="/images/svg/search.svg" alt="بحث" />
            </SearchIcon>
          </form>
        </SearchSection>
        
        <TrendingSection>
          <TrendingText>
            <a href="#" className="trending-label">الأغاني الرائجة :</a>
          </TrendingText>
          <TrendingMarquee>
            <a href="#">
              احلم بلحظاتك، حتى التقيت بك، امنحني بعض الشجاعة، الزقاق المظلم (+8 أخرى)
            </a>
          </TrendingMarquee>
        </TrendingSection>
      </HeaderLeft>

      <HeaderRight>
        <LanguageSection>
          <span>
            اللغات <img src="/images/svg/lang.svg" alt="اللغات" />
          </span>
        </LanguageSection>
        
        <ButtonSection>
          <HeaderButton className="register">
            <span>تسجيل</span>
          </HeaderButton>
          <HeaderButton className="login">
            <span>دخول</span>
          </HeaderButton>
        </ButtonSection>
      </HeaderRight>
    </HeaderContainer>
  );
};

export default Header;
