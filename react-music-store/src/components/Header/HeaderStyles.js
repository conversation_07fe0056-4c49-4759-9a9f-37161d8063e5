import styled, { keyframes } from 'styled-components';

const marqueeAnimation = keyframes`
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
`;

export const HeaderContainer = styled.div`
  position: fixed;
  background-color: ${props => props.theme.colors.secondary};
  padding: 20px 50px;
  left: 0;
  right: 80px;
  top: 0;
  z-index: ${props => props.theme.zIndex.sticky};
  backface-visibility: hidden;
  box-shadow: ${props => props.theme.shadows.medium};
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: ${props => props.theme.spacing.lg};

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    right: 0;
    padding: 15px 20px;
    flex-direction: column;
    gap: ${props => props.theme.spacing.md};
  }
`;

export const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.xl};
  flex: 1;

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    width: 100%;
    flex-direction: column;
    gap: ${props => props.theme.spacing.md};
  }
`;

export const HeaderRight = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.lg};

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    width: 100%;
    justify-content: center;
  }
`;

export const SearchSection = styled.div`
  max-width: 395px;
  position: relative;
  display: flex;
  width: 100%;

  form {
    width: 100%;
    position: relative;
  }

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    max-width: 100%;
  }
`;

export const SearchInput = styled.input`
  width: 100%;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 5px 0 0 5px;
  padding: 0 60px 0 15px;
  color: ${props => props.theme.colors.white};
  font-family: ${props => props.theme.fonts.primary};
  font-size: ${props => props.theme.fontSizes.md};
  transition: ${props => props.theme.transitions.fast};

  &::placeholder {
    color: ${props => props.theme.colors.text};
  }

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    background-color: rgba(255, 255, 255, 0.15);
  }
`;

export const SearchIcon = styled.span`
  position: absolute;
  left: 0;
  top: 0;
  height: 40px;
  line-height: 40px;
  background-color: ${props => props.theme.colors.primary};
  border-radius: 0 5px 5px 0;
  padding: 0 16px;
  cursor: pointer;
  transition: ${props => props.theme.transitions.fast};
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: ${props => props.theme.colors.white};
    
    img {
      filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%);
    }
  }

  img {
    width: 18px;
    height: 18px;
    filter: brightness(0) invert(1);
  }
`;

export const TrendingSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
  flex: 1;
  min-width: 0;

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    width: 100%;
  }
`;

export const TrendingText = styled.span`
  white-space: nowrap;
  
  .trending-label {
    color: ${props => props.theme.colors.primary};
    font-weight: 500;
    text-decoration: none;
    
    &:hover {
      color: ${props => props.theme.colors.white};
    }
  }
`;

export const TrendingMarquee = styled.span`
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  
  a {
    display: inline-block;
    color: ${props => props.theme.colors.text};
    text-decoration: none;
    animation: ${marqueeAnimation} 20s linear infinite;
    padding-right: 100%;
    
    &:hover {
      color: ${props => props.theme.colors.primary};
      animation-play-state: paused;
    }
  }
`;

export const LanguageSection = styled.div`
  cursor: pointer;
  color: ${props => props.theme.colors.text};
  font-size: ${props => props.theme.fontSizes.md};
  transition: ${props => props.theme.transitions.fast};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};

  &:hover {
    color: ${props => props.theme.colors.primary};
  }

  img {
    width: 16px;
    height: 16px;
  }
`;

export const ButtonSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.md};
`;

export const HeaderButton = styled.button`
  background: linear-gradient(45deg, ${props => props.theme.colors.primary}, #2ba8c7);
  color: ${props => props.theme.colors.white};
  border: none;
  border-radius: ${props => props.theme.borderRadius.medium};
  padding: 8px 20px;
  font-family: ${props => props.theme.fonts.primary};
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: 500;
  cursor: pointer;
  transition: ${props => props.theme.transitions.fast};
  min-width: 80px;

  &:hover {
    transform: translateY(-1px);
    box-shadow: ${props => props.theme.shadows.medium};
  }

  &.register {
    background: transparent;
    border: 2px solid ${props => props.theme.colors.primary};
    color: ${props => props.theme.colors.primary};

    &:hover {
      background: ${props => props.theme.colors.primary};
      color: ${props => props.theme.colors.white};
    }
  }

  span {
    display: block;
  }

  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    padding: 6px 16px;
    font-size: ${props => props.theme.fontSizes.xs};
    min-width: 70px;
  }
`;
