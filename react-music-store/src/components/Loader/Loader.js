import React, { useState, useEffect } from 'react';
import styled, { keyframes } from 'styled-components';

const fadeOut = keyframes`
  from {
    opacity: 1;
    visibility: visible;
  }
  to {
    opacity: 0;
    visibility: hidden;
  }
`;

const LoaderWrapper = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: ${props => props.theme.colors.background};
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: ${props => props.theme.zIndex.modal};
  
  &.fade-out {
    animation: ${fadeOut} 0.5s ease forwards;
  }
`;

const LoaderContent = styled.div`
  text-align: center;
  color: ${props => props.theme.colors.white};
`;

const LoaderImage = styled.img`
  max-width: 200px;
  height: auto;
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const LoaderText = styled.h2`
  font-family: ${props => props.theme.fonts.arabic};
  color: ${props => props.theme.colors.primary};
  margin-bottom: ${props => props.theme.spacing.md};
`;

const LoaderSubtext = styled.p`
  color: ${props => props.theme.colors.text};
  font-size: ${props => props.theme.fontSizes.md};
`;

const Loader = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [fadeOut, setFadeOut] = useState(false);

  useEffect(() => {
    // محاكاة وقت التحميل
    const timer = setTimeout(() => {
      setFadeOut(true);
      // إخفاء اللودر بعد انتهاء الأنيميشن
      setTimeout(() => {
        setIsLoading(false);
      }, 500);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  if (!isLoading) {
    return null;
  }

  return (
    <LoaderWrapper className={fadeOut ? 'fade-out' : ''}>
      <LoaderContent>
        <LoaderImage src="/images/loader.gif" alt="جاري التحميل..." />
        <LoaderText>مرحباً بك في معجزة</LoaderText>
        <LoaderSubtext>متجر الموسيقى العربي</LoaderSubtext>
      </LoaderContent>
    </LoaderWrapper>
  );
};

export default Loader;
