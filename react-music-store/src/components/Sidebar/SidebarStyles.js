import styled from 'styled-components';

export const SidebarContainer = styled.div`
  position: fixed;
  width: 80px;
  background-color: ${props => props.theme.colors.secondary};
  height: 100%;
  z-index: ${props => props.theme.zIndex.fixed};
  top: 0;
  bottom: 0;
  right: 0;
  box-shadow: ${props => props.theme.shadows.custom};
  transition: width ${props => props.theme.transitions.medium};

  &.open {
    width: 240px;
  }

  &:hover {
    width: 240px;
  }

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    transform: translateX(100%);
    width: 240px;
    
    &.open {
      transform: translateX(0);
    }
  }
`;

export const CloseButton = styled.div`
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background-color: ${props => props.theme.colors.primary};
  border-radius: ${props => props.theme.borderRadius.round};
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: ${props => props.theme.transitions.fast};
  z-index: 10;

  i {
    color: ${props => props.theme.colors.white};
    font-size: 18px;
    transition: ${props => props.theme.transitions.fast};
  }

  &:hover {
    background-color: ${props => props.theme.colors.white};
    
    i {
      color: ${props => props.theme.colors.primary};
    }
  }

  @media (min-width: ${props => props.theme.breakpoints.tablet}) {
    display: none;
  }
`;

export const SidebarInner = styled.div`
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding: ${props => props.theme.spacing.lg} 0;

  /* شريط التمرير المخصص */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: ${props => props.theme.colors.primary};
    border-radius: ${props => props.theme.borderRadius.medium};
  }
`;

export const LogoSection = styled.div`
  padding: 0 ${props => props.theme.spacing.lg};
  margin-bottom: ${props => props.theme.spacing.xl};
  text-align: center;
`;

export const Logo = styled.div`
  display: block;
  
  ${SidebarContainer}:hover & {
    display: none;
  }
  
  ${SidebarContainer}.open & {
    display: none;
  }

  img {
    max-width: 50px;
    height: auto;
    margin: 0 auto;
  }
`;

export const LogoOpen = styled.div`
  display: none;
  
  ${SidebarContainer}:hover & {
    display: block;
  }
  
  ${SidebarContainer}.open & {
    display: block;
  }

  img {
    max-width: 150px;
    height: auto;
    margin: 0 auto;
  }
`;

export const NavWrapper = styled.div`
  padding: 0 ${props => props.theme.spacing.md};
`;

export const NavList = styled.ul`
  list-style: none;
  margin-bottom: ${props => props.theme.spacing.xl};

  &.nav_downloads {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: ${props => props.theme.spacing.lg};
  }

  &.nav_playlist {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: ${props => props.theme.spacing.lg};
  }

  &.nav_others {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: ${props => props.theme.spacing.lg};
  }
`;

export const NavItem = styled.li`
  margin-bottom: ${props => props.theme.spacing.sm};
`;

export const NavLink = styled.a`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.spacing.md};
  color: ${props => props.theme.colors.text};
  text-decoration: none;
  border-radius: ${props => props.theme.borderRadius.medium};
  transition: ${props => props.theme.transitions.fast};
  position: relative;
  overflow: hidden;

  &:hover {
    background-color: rgba(59, 200, 231, 0.1);
    color: ${props => props.theme.colors.primary};
    transform: translateX(-2px);
  }

  &.active {
    background-color: ${props => props.theme.colors.primary};
    color: ${props => props.theme.colors.white};
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 4px;
      height: 100%;
      background-color: ${props => props.theme.colors.white};
    }
  }
`;

export const NavIcon = styled.span`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin-left: ${props => props.theme.spacing.md};
  flex-shrink: 0;

  .icon {
    width: 24px;
    height: 24px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.8;
    transition: ${props => props.theme.transitions.fast};
  }

  ${NavLink}:hover & .icon,
  ${NavLink}.active & .icon {
    opacity: 1;
  }

  /* أيقونات SVG - يمكن استبدالها بأيقونات Font Awesome */
  .icon_discover { background-image: url('/images/svg/discover.svg'); }
  .icon_albums { background-image: url('/images/svg/albums.svg'); }
  .icon_artists { background-image: url('/images/svg/artists.svg'); }
  .icon_genres { background-image: url('/images/svg/genres.svg'); }
  .icon_tracks { background-image: url('/images/svg/tracks.svg'); }
  .icon_music { background-image: url('/images/svg/music.svg'); }
  .icon_station { background-image: url('/images/svg/station.svg'); }
  .icon_download { background-image: url('/images/svg/download.svg'); }
  .icon_purchased { background-image: url('/images/svg/purchased.svg'); }
  .icon_favourite { background-image: url('/images/svg/favourite.svg'); }
  .icon_history { background-image: url('/images/svg/history.svg'); }
  .icon_fe_playlist { background-image: url('/images/svg/playlist.svg'); }
  .icon_c_playlist { background-image: url('/images/svg/create.svg'); }
  .icon_profile { background-image: url('/images/svg/profile.svg'); }
  .icon_upload { background-image: url('/images/svg/upload.svg'); }
  .icon_blog { background-image: url('/images/svg/blog.svg'); }
`;

export const NavText = styled.span`
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  transform: translateX(20px);
  transition: all ${props => props.theme.transitions.medium};

  ${SidebarContainer}:hover & {
    opacity: 1;
    transform: translateX(0);
  }
  
  ${SidebarContainer}.open & {
    opacity: 1;
    transform: translateX(0);
  }
`;
