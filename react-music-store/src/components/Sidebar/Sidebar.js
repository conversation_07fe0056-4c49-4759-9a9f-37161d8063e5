import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  SidebarContainer,
  SidebarInner,
  LogoSection,
  Logo,
  LogoOpen,
  NavWrapper,
  NavList,
  NavItem,
  NavLink,
  NavIcon,
  NavText,
  CloseButton
} from './SidebarStyles';

const Sidebar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();

  const mainNavItems = [
    { path: '/', icon: 'discover', text: 'اكتشف', title: 'اكتشف' },
    { path: '/albums', icon: 'albums', text: 'الألبومات', title: 'الألبومات' },
    { path: '/artists', icon: 'artists', text: 'الفنانين', title: 'الفنانين' },
    { path: '/genres', icon: 'genres', text: 'الأنواع', title: 'الأنواع' },
    { path: '/top-tracks', icon: 'tracks', text: 'أفضل الأغاني', title: 'أفضل الأغاني' },
    { path: '/free-music', icon: 'music', text: 'موسيقى مجانية', title: 'موسيقى مجانية' },
    { path: '/stations', icon: 'station', text: 'المحطات', title: 'المحطات' }
  ];

  const downloadNavItems = [
    { path: '/downloads', icon: 'download', text: 'التحميلات', title: 'التحميلات' },
    { path: '/purchases', icon: 'purchased', text: 'المشتريات', title: 'المشتريات' },
    { path: '/favourites', icon: 'favourite', text: 'المفضلة', title: 'المفضلة' },
    { path: '/history', icon: 'history', text: 'التاريخ', title: 'التاريخ' }
  ];

  const playlistNavItems = [
    { path: '/featured-playlists', icon: 'fe_playlist', text: 'قوائم مميزة', title: 'قوائم التشغيل المميزة' },
    { path: '/create-playlist', icon: 'c_playlist', text: 'إنشاء قائمة', title: 'إنشاء قائمة تشغيل' }
  ];

  const otherNavItems = [
    { path: '/profile', icon: 'profile', text: 'الملف الشخصي', title: 'الملف الشخصي' },
    { path: '/upload', icon: 'upload', text: 'رفع الملفات', title: 'رفع الملفات' },
    { path: '/blog', icon: 'blog', text: 'المدونة', title: 'المدونة' }
  ];

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
  };

  const renderNavItems = (items, className = '') => {
    return (
      <NavList className={className}>
        {items.map((item, index) => (
          <NavItem key={index}>
            <NavLink
              as={Link}
              to={item.path}
              className={location.pathname === item.path ? 'active' : ''}
              title={item.title}
              onClick={() => setIsOpen(false)}
            >
              <NavIcon>
                <span className={`icon icon_${item.icon}`}></span>
              </NavIcon>
              <NavText>
                {item.text}
              </NavText>
            </NavLink>
          </NavItem>
        ))}
      </NavList>
    );
  };

  return (
    <SidebarContainer className={isOpen ? 'open' : ''}>
      <CloseButton onClick={toggleSidebar}>
        <i className="fa fa-angle-left" aria-hidden="true"></i>
      </CloseButton>
      
      <SidebarInner>
        <LogoSection>
          <Logo>
            <Link to="/">
              <img src="/images/logo.png" alt="معجزة" />
            </Link>
          </Logo>
          <LogoOpen>
            <Link to="/">
              <img src="/images/open_logo.png" alt="معجزة" />
            </Link>
          </LogoOpen>
        </LogoSection>

        <NavWrapper>
          {/* القائمة الرئيسية */}
          {renderNavItems(mainNavItems)}
          
          {/* قائمة التحميلات */}
          {renderNavItems(downloadNavItems, 'nav_downloads')}
          
          {/* قوائم التشغيل */}
          {renderNavItems(playlistNavItems, 'nav_playlist')}
          
          {/* أخرى */}
          {renderNavItems(otherNavItems, 'nav_others')}
        </NavWrapper>
      </SidebarInner>
    </SidebarContainer>
  );
};

export default Sidebar;
