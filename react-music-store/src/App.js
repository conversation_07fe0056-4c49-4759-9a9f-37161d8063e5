import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import GlobalStyles from './styles/GlobalStyles';
import theme from './styles/theme';

// Components
import Sidebar from './components/Sidebar/Sidebar';
import Header from './components/Header/Header';
import Loader from './components/Loader/Loader';

// Pages
import Home from './pages/Home/Home';
import Albums from './pages/Albums/Albums';
import Artists from './pages/Artists/Artists';
import Genres from './pages/Genres/Genres';
import TopTracks from './pages/TopTracks/TopTracks';
import FreeMusic from './pages/FreeMusic/FreeMusic';
import Stations from './pages/Stations/Stations';

// Styled Components
import { AppContainer, MainWrapper, ContentWrapper } from './styles/AppStyles';

function App() {
  return (
    <ThemeProvider theme={theme}>
      <GlobalStyles />
      <Router>
        <AppContainer dir="rtl">
          <Loader />
          <MainWrapper>
            <Sidebar />
            <ContentWrapper>
              <Header />
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/albums" element={<Albums />} />
                <Route path="/artists" element={<Artists />} />
                <Route path="/genres" element={<Genres />} />
                <Route path="/top-tracks" element={<TopTracks />} />
                <Route path="/free-music" element={<FreeMusic />} />
                <Route path="/stations" element={<Stations />} />
              </Routes>
            </ContentWrapper>
          </MainWrapper>
        </AppContainer>
      </Router>
    </ThemeProvider>
  );
}

export default App;
