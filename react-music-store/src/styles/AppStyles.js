import styled from 'styled-components';

export const AppContainer = styled.div`
  min-height: 100vh;
  background-color: ${props => props.theme.colors.background};
  direction: rtl;
  font-family: ${props => props.theme.fonts.primary};
`;

export const MainWrapper = styled.div`
  display: flex;
  min-height: 100vh;
  position: relative;
`;

export const ContentWrapper = styled.div`
  flex: 1;
  margin-right: 80px;
  margin-left: 160px;
  padding-top: 80px;
  transition: ${props => props.theme.transitions.medium};

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    margin-right: 0;
    margin-left: 0;
    padding-top: 60px;
  }
`;

export const LoaderWrapper = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: ${props => props.theme.colors.background};
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: ${props => props.theme.zIndex.modal};
  
  &.fade-out {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.5s ease, visibility 0.5s ease;
  }
`;

export const LoaderImage = styled.img`
  max-width: 200px;
  height: auto;
`;

// أنماط الأزرار المشتركة
export const Button = styled.button`
  background: linear-gradient(45deg, ${props => props.theme.colors.primary}, #2ba8c7);
  color: ${props => props.theme.colors.white};
  border: none;
  border-radius: ${props => props.theme.borderRadius.medium};
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.lg};
  font-family: ${props => props.theme.fonts.primary};
  font-size: ${props => props.theme.fontSizes.md};
  font-weight: 500;
  cursor: pointer;
  transition: ${props => props.theme.transitions.fast};
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: ${props => props.theme.spacing.sm};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.shadows.medium};
    background: linear-gradient(45deg, #2ba8c7, ${props => props.theme.colors.primary});
  }

  &:active {
    transform: translateY(0);
  }

  &.secondary {
    background: transparent;
    border: 2px solid ${props => props.theme.colors.primary};
    color: ${props => props.theme.colors.primary};

    &:hover {
      background: ${props => props.theme.colors.primary};
      color: ${props => props.theme.colors.white};
    }
  }

  &.small {
    padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.md};
    font-size: ${props => props.theme.fontSizes.sm};
  }

  &.large {
    padding: ${props => props.theme.spacing.md} ${props => props.theme.spacing.xl};
    font-size: ${props => props.theme.fontSizes.lg};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:hover {
      transform: none;
      box-shadow: none;
    }
  }
`;

// أنماط البطاقات المشتركة
export const Card = styled.div`
  background-color: ${props => props.theme.colors.secondary};
  border-radius: ${props => props.theme.borderRadius.large};
  padding: ${props => props.theme.spacing.lg};
  box-shadow: ${props => props.theme.shadows.medium};
  transition: ${props => props.theme.transitions.medium};

  &:hover {
    transform: translateY(-4px);
    box-shadow: ${props => props.theme.shadows.large};
  }
`;

// أنماط الشبكة
export const Grid = styled.div`
  display: grid;
  gap: ${props => props.theme.spacing.lg};
  
  &.cols-1 {
    grid-template-columns: 1fr;
  }
  
  &.cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  &.cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  &.cols-4 {
    grid-template-columns: repeat(4, 1fr);
  }

  @media (max-width: ${props => props.theme.breakpoints.desktop}) {
    &.cols-4 {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    &.cols-3,
    &.cols-4 {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    &.cols-2,
    &.cols-3,
    &.cols-4 {
      grid-template-columns: 1fr;
    }
  }
`;

// أنماط الفليكس
export const Flex = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing.md};
  
  &.column {
    flex-direction: column;
  }
  
  &.center {
    justify-content: center;
    align-items: center;
  }
  
  &.between {
    justify-content: space-between;
  }
  
  &.around {
    justify-content: space-around;
  }
  
  &.wrap {
    flex-wrap: wrap;
  }
`;

// أنماط النصوص
export const Text = styled.p`
  color: ${props => props.color || props.theme.colors.text};
  font-size: ${props => props.size || props.theme.fontSizes.md};
  font-weight: ${props => props.weight || 400};
  line-height: ${props => props.lineHeight || 1.6};
  margin-bottom: ${props => props.mb || props.theme.spacing.md};
  text-align: ${props => props.align || 'right'};
  
  &.primary {
    color: ${props => props.theme.colors.primary};
  }
  
  &.white {
    color: ${props => props.theme.colors.white};
  }
  
  &.center {
    text-align: center;
  }
  
  &.bold {
    font-weight: 600;
  }
`;
