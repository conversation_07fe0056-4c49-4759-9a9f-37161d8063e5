import { createGlobalStyle } from 'styled-components';

const GlobalStyles = createGlobalStyle`
  /* استيراد الخطوط العربية من Google Fonts */
  @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap');

  /* إعادة تعيين الأنماط */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    font-size: 16px;
    scroll-behavior: smooth;
  }

  body {
    font-family: ${props => props.theme.fonts.primary};
    font-size: ${props => props.theme.fontSizes.md};
    font-weight: 400;
    line-height: 1.5;
    color: ${props => props.theme.colors.text};
    background-color: ${props => props.theme.colors.background};
    direction: rtl;
    text-align: right;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* العناوين */
  h1, h2, h3, h4, h5, h6 {
    font-family: ${props => props.theme.fonts.arabic};
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: ${props => props.theme.spacing.md};
    color: ${props => props.theme.colors.white};
  }

  h1 {
    font-size: ${props => props.theme.fontSizes.xxxl};
  }

  h2 {
    font-size: ${props => props.theme.fontSizes.xxl};
  }

  h3 {
    font-size: ${props => props.theme.fontSizes.xl};
  }

  h4 {
    font-size: ${props => props.theme.fontSizes.lg};
  }

  h5 {
    font-size: ${props => props.theme.fontSizes.md};
  }

  h6 {
    font-size: ${props => props.theme.fontSizes.sm};
  }

  /* الفقرات */
  p {
    margin-bottom: ${props => props.theme.spacing.md};
    line-height: 1.6;
  }

  /* الروابط */
  a {
    color: ${props => props.theme.colors.primary};
    text-decoration: none;
    transition: ${props => props.theme.transitions.fast};

    &:hover {
      color: ${props => props.theme.colors.white};
      text-decoration: none;
    }
  }

  /* الأزرار */
  button {
    font-family: ${props => props.theme.fonts.primary};
    border: none;
    outline: none;
    cursor: pointer;
    transition: ${props => props.theme.transitions.fast};
  }

  /* حقول الإدخال */
  input, textarea, select {
    font-family: ${props => props.theme.fonts.primary};
    border: none;
    outline: none;
    background: transparent;
  }

  /* القوائم */
  ul, ol {
    list-style: none;
  }

  /* الصور */
  img {
    max-width: 100%;
    height: auto;
    display: block;
  }

  /* شريط التمرير المخصص */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: ${props => props.theme.colors.secondary};
  }

  ::-webkit-scrollbar-thumb {
    background: ${props => props.theme.colors.primary};
    border-radius: ${props => props.theme.borderRadius.medium};
  }

  ::-webkit-scrollbar-thumb:hover {
    background: ${props => props.theme.colors.white};
  }

  /* الفئات المساعدة */
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 ${props => props.theme.spacing.md};
  }

  .text-center {
    text-align: center;
  }

  .text-right {
    text-align: right;
  }

  .text-left {
    text-align: left;
  }

  .d-none {
    display: none;
  }

  .d-block {
    display: block;
  }

  .d-flex {
    display: flex;
  }

  .justify-center {
    justify-content: center;
  }

  .align-center {
    align-items: center;
  }

  .mb-0 { margin-bottom: 0; }
  .mb-1 { margin-bottom: ${props => props.theme.spacing.xs}; }
  .mb-2 { margin-bottom: ${props => props.theme.spacing.sm}; }
  .mb-3 { margin-bottom: ${props => props.theme.spacing.md}; }
  .mb-4 { margin-bottom: ${props => props.theme.spacing.lg}; }
  .mb-5 { margin-bottom: ${props => props.theme.spacing.xl}; }

  .mt-0 { margin-top: 0; }
  .mt-1 { margin-top: ${props => props.theme.spacing.xs}; }
  .mt-2 { margin-top: ${props => props.theme.spacing.sm}; }
  .mt-3 { margin-top: ${props => props.theme.spacing.md}; }
  .mt-4 { margin-top: ${props => props.theme.spacing.lg}; }
  .mt-5 { margin-top: ${props => props.theme.spacing.xl}; }

  /* التصميم المتجاوب */
  @media (max-width: ${props => props.theme.breakpoints.tablet}) {
    body {
      font-size: ${props => props.theme.fontSizes.sm};
    }

    h1 {
      font-size: ${props => props.theme.fontSizes.xxl};
    }

    h2 {
      font-size: ${props => props.theme.fontSizes.xl};
    }

    h3 {
      font-size: ${props => props.theme.fontSizes.lg};
    }
  }

  @media (max-width: ${props => props.theme.breakpoints.mobile}) {
    .container {
      padding: 0 ${props => props.theme.spacing.sm};
    }
  }
`;
