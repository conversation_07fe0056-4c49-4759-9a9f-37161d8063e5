const theme = {
  // الألوان الرئيسية
  colors: {
    primary: '#3bc8e7',
    secondary: '#1b2039',
    background: '#14182a',
    text: '#777777',
    white: '#ffffff',
    black: '#000000',
    gray: '#f8f9fa',
    darkGray: '#343a40',
    success: '#28a745',
    warning: '#ffc107',
    danger: '#dc3545',
    info: '#17a2b8'
  },

  // الخطوط
  fonts: {
    primary: "'Cairo', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', sans-serif",
    secondary: "'<PERSON><PERSON>', sans-serif",
    arabic: "'Cairo', 'Tajawal', 'Amiri', sans-serif"
  },

  // أحجام الخطوط
  fontSizes: {
    xs: '12px',
    sm: '14px',
    md: '16px',
    lg: '18px',
    xl: '20px',
    xxl: '24px',
    xxxl: '32px'
  },

  // المسافات
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px',
    xxxl: '64px'
  },

  // نقاط الكسر للتصميم المتجاوب
  breakpoints: {
    mobile: '480px',
    tablet: '768px',
    desktop: '1024px',
    largeDesktop: '1200px'
  },

  // الظلال
  shadows: {
    small: '0px 2px 4px rgba(0, 0, 0, 0.1)',
    medium: '0px 4px 8px rgba(0, 0, 0, 0.15)',
    large: '0px 8px 16px rgba(0, 0, 0, 0.2)',
    custom: '0px 0px 24px 6px rgba(4, 4, 4, 0.2)'
  },

  // الحدود المدورة
  borderRadius: {
    small: '4px',
    medium: '8px',
    large: '12px',
    round: '50%'
  },

  // الانتقالات
  transitions: {
    fast: '0.2s ease',
    medium: '0.3s ease',
    slow: '0.5s ease'
  },

  // Z-index
  zIndex: {
    dropdown: 1000,
    sticky: 1020,
    fixed: 1030,
    modal: 1040,
    popover: 1050,
    tooltip: 1060
  }
};

export default theme;
