import React, { useState, useEffect } from 'react';
import Banner from '../../components/Banner/Banner';
import RecentlyPlayed from '../../components/RecentlyPlayed/RecentlyPlayed';
import WeeklyTop from '../../components/WeeklyTop/WeeklyTop';
import { HomeContainer } from './HomeStyles';

// خدمة WordPress API
import { getRecentTracks, getTopTracks, getFeaturedAlbums } from '../../services/wordpressApi';

const Home = () => {
  const [recentTracks, setRecentTracks] = useState([]);
  const [topTracks, setTopTracks] = useState([]);
  const [featuredAlbums, setFeaturedAlbums] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // جلب البيانات من WordPress
        const [recent, top, featured] = await Promise.all([
          getRecentTracks(),
          getTopTracks(),
          getFeaturedAlbums()
        ]);

        setRecentTracks(recent);
        setTopTracks(top);
        setFeaturedAlbums(featured);
      } catch (error) {
        console.error('خطأ في جلب البيانات:', error);
        
        // بيانات تجريبية في حالة فشل الاتصال
        setRecentTracks([
          {
            id: 1,
            title: 'احلم بلحظاتك (ثنائي)',
            artist: 'آفا كورنيش وبرايان هيل',
            image: '/images/music/r_music1.jpg',
            duration: '3:45'
          },
          {
            id: 2,
            title: 'حتى التقيت بك',
            artist: 'آفا كورنيش وبرايان هيل',
            image: '/images/music/r_music2.jpg',
            duration: '4:12'
          },
          {
            id: 3,
            title: 'امنحني بعض الشجاعة',
            artist: 'آفا كورنيش وبرايان هيل',
            image: '/images/music/r_music3.jpg',
            duration: '3:28'
          },
          {
            id: 4,
            title: 'الزقاق المظلم الصوتي',
            artist: 'آفا كورنيش وبرايان هيل',
            image: '/images/music/r_music4.jpg',
            duration: '4:05'
          },
          {
            id: 5,
            title: 'وعود المشي',
            artist: 'آفا كورنيش وبرايان هيل',
            image: '/images/music/r_music5.jpg',
            duration: '3:52'
          }
        ]);

        setTopTracks([
          {
            id: 1,
            rank: 1,
            title: 'حتى التقيت بك',
            artist: 'آفا كورنيش',
            image: '/images/weekly/song1.jpg',
            duration: '5:10'
          },
          {
            id: 2,
            rank: 2,
            title: 'وعود المشي',
            artist: 'آفا كورنيش',
            image: '/images/weekly/song2.jpg',
            duration: '4:32'
          },
          {
            id: 3,
            rank: 3,
            title: 'امنحني بعض الشجاعة',
            artist: 'آفا كورنيش',
            image: '/images/weekly/song3.jpg',
            duration: '3:45'
          },
          {
            id: 4,
            rank: 4,
            title: 'الألعاب المرغوبة',
            artist: 'آفا كورنيش',
            image: '/images/weekly/song4.jpg',
            duration: '4:18'
          },
          {
            id: 5,
            rank: 5,
            title: 'الزقاق المظلم الصوتي',
            artist: 'آفا كورنيش',
            image: '/images/weekly/song5.jpg',
            duration: '3:55'
          }
        ]);

        setFeaturedAlbums([
          {
            id: 1,
            title: 'ألبومات هذا الشهر',
            subtitle: 'الأكثر نجاحاً وإبداعاً !',
            description: 'احلم بلحظاتك، حتى التقيت بك، امنحني بعض الشجاعة، الزقاق المظلم، واحد أكثر من غريب، أشياء لا نهائية الأشياء، توقف نبضات القلب، وعود المشي، الألعاب المرغوبة والمزيد...',
            image: '/images/banner.png'
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <HomeContainer>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '400px',
          color: '#3bc8e7',
          fontSize: '18px'
        }}>
          جاري تحميل المحتوى...
        </div>
      </HomeContainer>
    );
  }

  return (
    <HomeContainer>
      {/* البانر الرئيسي */}
      <Banner albums={featuredAlbums} />
      
      {/* الأغاني المشغلة مؤخراً */}
      <RecentlyPlayed tracks={recentTracks} />
      
      {/* أفضل 15 أغنية هذا الأسبوع */}
      <WeeklyTop tracks={topTracks} />
    </HomeContainer>
  );
};

export default Home;
