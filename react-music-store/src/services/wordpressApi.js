import axios from 'axios';

// إعداد الـ Base URL لـ WordPress API
const WP_API_BASE_URL = process.env.REACT_APP_WP_API_URL || 'http://localhost/wordpress/wp-json/wp/v2';

// إنشاء instance من axios
const api = axios.create({
  baseURL: WP_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// إضافة interceptor للتعامل مع الأخطاء
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('خطأ في API:', error);
    return Promise.reject(error);
  }
);

// دوال جلب البيانات من WordPress

/**
 * جلب الأغاني المشغلة مؤخراً
 */
export const getRecentTracks = async () => {
  try {
    const response = await api.get('/tracks', {
      params: {
        per_page: 10,
        orderby: 'date',
        order: 'desc',
        _embed: true
      }
    });
    
    return response.data.map(track => ({
      id: track.id,
      title: track.title.rendered,
      artist: track.acf?.artist || 'فنان غير معروف',
      image: track._embedded?.['wp:featuredmedia']?.[0]?.source_url || '/images/default-track.jpg',
      duration: track.acf?.duration || '0:00',
      audio_url: track.acf?.audio_file,
      genre: track.acf?.genre,
      album: track.acf?.album
    }));
  } catch (error) {
    console.error('خطأ في جلب الأغاني المشغلة مؤخراً:', error);
    throw error;
  }
};

/**
 * جلب أفضل الأغاني
 */
export const getTopTracks = async () => {
  try {
    const response = await api.get('/tracks', {
      params: {
        per_page: 15,
        meta_key: 'play_count',
        orderby: 'meta_value_num',
        order: 'desc',
        _embed: true
      }
    });
    
    return response.data.map((track, index) => ({
      id: track.id,
      rank: index + 1,
      title: track.title.rendered,
      artist: track.acf?.artist || 'فنان غير معروف',
      image: track._embedded?.['wp:featuredmedia']?.[0]?.source_url || '/images/default-track.jpg',
      duration: track.acf?.duration || '0:00',
      play_count: track.acf?.play_count || 0,
      audio_url: track.acf?.audio_file
    }));
  } catch (error) {
    console.error('خطأ في جلب أفضل الأغاني:', error);
    throw error;
  }
};

/**
 * جلب الألبومات المميزة
 */
export const getFeaturedAlbums = async () => {
  try {
    const response = await api.get('/albums', {
      params: {
        per_page: 5,
        meta_key: 'featured',
        meta_value: true,
        _embed: true
      }
    });
    
    return response.data.map(album => ({
      id: album.id,
      title: album.title.rendered,
      subtitle: album.acf?.subtitle,
      description: album.excerpt.rendered,
      image: album._embedded?.['wp:featuredmedia']?.[0]?.source_url || '/images/default-album.jpg',
      artist: album.acf?.artist,
      release_date: album.acf?.release_date,
      tracks_count: album.acf?.tracks_count
    }));
  } catch (error) {
    console.error('خطأ في جلب الألبومات المميزة:', error);
    throw error;
  }
};

/**
 * جلب الفنانين
 */
export const getArtists = async () => {
  try {
    const response = await api.get('/artists', {
      params: {
        per_page: 20,
        orderby: 'title',
        order: 'asc',
        _embed: true
      }
    });
    
    return response.data.map(artist => ({
      id: artist.id,
      name: artist.title.rendered,
      bio: artist.content.rendered,
      image: artist._embedded?.['wp:featuredmedia']?.[0]?.source_url || '/images/default-artist.jpg',
      genre: artist.acf?.genre,
      albums_count: artist.acf?.albums_count,
      followers: artist.acf?.followers
    }));
  } catch (error) {
    console.error('خطأ في جلب الفنانين:', error);
    throw error;
  }
};

/**
 * جلب الأنواع الموسيقية
 */
export const getGenres = async () => {
  try {
    const response = await api.get('/genres', {
      params: {
        per_page: 50,
        orderby: 'name',
        order: 'asc'
      }
    });
    
    return response.data.map(genre => ({
      id: genre.id,
      name: genre.name,
      description: genre.description,
      count: genre.count,
      slug: genre.slug
    }));
  } catch (error) {
    console.error('خطأ في جلب الأنواع الموسيقية:', error);
    throw error;
  }
};

/**
 * البحث في المحتوى
 */
export const searchContent = async (query, type = 'tracks') => {
  try {
    const response = await api.get(`/${type}`, {
      params: {
        search: query,
        per_page: 20,
        _embed: true
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('خطأ في البحث:', error);
    throw error;
  }
};

/**
 * جلب تفاصيل أغنية واحدة
 */
export const getTrackById = async (id) => {
  try {
    const response = await api.get(`/tracks/${id}`, {
      params: {
        _embed: true
      }
    });
    
    const track = response.data;
    return {
      id: track.id,
      title: track.title.rendered,
      content: track.content.rendered,
      artist: track.acf?.artist || 'فنان غير معروف',
      image: track._embedded?.['wp:featuredmedia']?.[0]?.source_url || '/images/default-track.jpg',
      duration: track.acf?.duration || '0:00',
      audio_url: track.acf?.audio_file,
      genre: track.acf?.genre,
      album: track.acf?.album,
      release_date: track.acf?.release_date,
      play_count: track.acf?.play_count || 0
    };
  } catch (error) {
    console.error('خطأ في جلب تفاصيل الأغنية:', error);
    throw error;
  }
};

/**
 * إضافة أغنية للمفضلة
 */
export const addToFavorites = async (trackId, userId) => {
  try {
    const response = await api.post('/favorites', {
      track_id: trackId,
      user_id: userId
    });
    
    return response.data;
  } catch (error) {
    console.error('خطأ في إضافة الأغنية للمفضلة:', error);
    throw error;
  }
};

/**
 * إزالة أغنية من المفضلة
 */
export const removeFromFavorites = async (trackId, userId) => {
  try {
    const response = await api.delete(`/favorites/${trackId}`, {
      data: { user_id: userId }
    });
    
    return response.data;
  } catch (error) {
    console.error('خطأ في إزالة الأغنية من المفضلة:', error);
    throw error;
  }
};

/**
 * تسجيل تشغيل أغنية
 */
export const recordPlay = async (trackId) => {
  try {
    const response = await api.post(`/tracks/${trackId}/play`);
    return response.data;
  } catch (error) {
    console.error('خطأ في تسجيل التشغيل:', error);
    throw error;
  }
};

export default api;
