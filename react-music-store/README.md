# 🎵 متجر الموسيقى العربي - React + WordPress

مشروع متجر موسيقى إلكتروني باللغة العربية مبني بـ React.js مع WordPress كـ Headless CMS.

## 🚀 **المميزات**

- ✅ **واجهة عربية كاملة** مع دعم RTL
- ✅ **تصميم متجاوب** يعمل على جميع الأجهزة
- ✅ **WordPress كـ Backend** مع REST API
- ✅ **مشغل موسيقى متقدم**
- ✅ **نظام بحث ذكي**
- ✅ **إدارة قوائم التشغيل**
- ✅ **نظام المفضلة**
- ✅ **إحصائيات التشغيل**

## 🛠️ **التقنيات المستخدمة**

### Frontend:
- React.js 18
- React Router DOM
- Styled Components
- Axios
- Swiper.js

### Backend:
- WordPress (Headless CMS)
- WordPress REST API
- Custom Post Types
- Custom Fields

## 📦 **التثبيت والإعداد**

### 1. إعداد React App:

```bash
# استنساخ المشروع
git clone [repository-url]
cd react-music-store

# تثبيت المكتبات
npm install

# تشغيل التطبيق
npm start
```

### 2. إعداد WordPress:

1. **تثبيت WordPress:**
```bash
# تحميل WordPress
wget https://wordpress.org/latest.zip
unzip latest.zip
```

2. **إضافة الكود إلى functions.php:**
```php
// نسخ محتوى ملف wordpress-setup/functions.php
// إلى ملف functions.php في الثيم النشط
```

3. **تفعيل REST API:**
```php
// إضافة هذا الكود إلى wp-config.php
define('WP_REST_API', true);
```

### 3. إعداد متغيرات البيئة:

إنشاء ملف `.env` في مجلد React:
```env
REACT_APP_WP_API_URL=http://localhost/wordpress/wp-json/wp/v2
REACT_APP_WP_MEDIA_URL=http://localhost/wordpress/wp-content/uploads
```

## 🗂️ **بنية المشروع**

```
react-music-store/
├── public/
│   ├── images/
│   └── index.html
├── src/
│   ├── components/
│   │   ├── Sidebar/
│   │   ├── Header/
│   │   ├── Banner/
│   │   ├── RecentlyPlayed/
│   │   └── WeeklyTop/
│   ├── pages/
│   │   ├── Home/
│   │   ├── Albums/
│   │   ├── Artists/
│   │   └── Genres/
│   ├── services/
│   │   └── wordpressApi.js
│   ├── styles/
│   │   ├── GlobalStyles.js
│   │   ├── theme.js
│   │   └── AppStyles.js
│   └── App.js
├── wordpress-setup/
│   └── functions.php
└── package.json
```

## 🎨 **التخصيص**

### الألوان:
```javascript
// في src/styles/theme.js
colors: {
  primary: '#3bc8e7',
  secondary: '#1b2039',
  background: '#14182a',
  // ...
}
```

### الخطوط:
```javascript
// في src/styles/theme.js
fonts: {
  primary: "'Cairo', 'Tajawal', 'Amiri', sans-serif",
  // ...
}
```

## 📊 **WordPress Custom Post Types**

### الأغاني (Tracks):
- العنوان
- الفنان
- المدة
- ملف الصوت
- الألبوم
- تاريخ الإصدار
- عدد مرات التشغيل

### الألبومات (Albums):
- العنوان
- الفنان
- تاريخ الإصدار
- عدد الأغاني
- مميز (نعم/لا)

### الفنانين (Artists):
- الاسم
- السيرة الذاتية
- النوع الموسيقي
- عدد الألبومات
- عدد المتابعين

## 🔌 **API Endpoints**

```javascript
// الأغاني
GET /wp-json/wp/v2/tracks
GET /wp-json/wp/v2/tracks/{id}
POST /wp-json/wp/v2/tracks/{id}/play

// الألبومات
GET /wp-json/wp/v2/albums
GET /wp-json/wp/v2/albums/{id}

// الفنانين
GET /wp-json/wp/v2/artists
GET /wp-json/wp/v2/artists/{id}

// الأنواع الموسيقية
GET /wp-json/wp/v2/genres
```

## 🚀 **النشر**

### Frontend (React):
```bash
# بناء التطبيق للإنتاج
npm run build

# رفع ملفات build إلى الخادم
```

### Backend (WordPress):
```bash
# رفع ملفات WordPress إلى الخادم
# تكوين قاعدة البيانات
# تفعيل الثيم والإضافات
```

## 🔧 **التطوير**

### إضافة مكون جديد:
```bash
# إنشاء مجلد المكون
mkdir src/components/NewComponent

# إنشاء الملفات
touch src/components/NewComponent/NewComponent.js
touch src/components/NewComponent/NewComponentStyles.js
```

### إضافة صفحة جديدة:
```bash
# إنشاء مجلد الصفحة
mkdir src/pages/NewPage

# إنشاء الملفات
touch src/pages/NewPage/NewPage.js
touch src/pages/NewPage/NewPageStyles.js
```

## 🐛 **استكشاف الأخطاء**

### مشاكل شائعة:

1. **خطأ CORS:**
```php
// إضافة هذا الكود إلى functions.php
add_action('rest_api_init', function() {
    header('Access-Control-Allow-Origin: *');
});
```

2. **عدم ظهور Custom Post Types:**
```php
// التأكد من إضافة show_in_rest => true
'show_in_rest' => true,
```

3. **مشاكل الخطوط العربية:**
```css
/* التأكد من استيراد الخطوط */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap');
```

## 📞 **الدعم**

للحصول على المساعدة:
- فتح issue في GitHub
- مراجعة الوثائق
- التواصل مع فريق التطوير

## 📄 **الترخيص**

هذا المشروع مرخص تحت رخصة MIT.

---

**🎉 استمتع ببناء متجر الموسيقى العربي!**
