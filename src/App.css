/* استيراد الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

/* إعادة تعيين الأساسيات */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', '<PERSON><PERSON>wal', '<PERSON><PERSON>', sans-serif;
  direction: rtl;
  text-align: right;
  background-color: #14182a;
  color: #777777;
  line-height: 1.6;
}

.App {
  min-height: 100vh;
  direction: rtl;
}



/* المحتوى الرئيسي */
.main-content {
  margin-right: 80px;
  margin-left: 160px;
  min-height: 100vh;
  transition: all 0.3s ease;
}

.page-content {
  padding: 20px;
  margin-top: 80px;
  margin-bottom: 100px; /* مساحة لمشغل الموسيقى */
}

/* تحسينات للنصوص العربية */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Cairo', 'Tajawal', sans-serif;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.4;
}

p, span, div {
  font-family: 'Cairo', 'Tajawal', sans-serif;
  line-height: 1.6;
}

/* الأزرار */
.btn {
  font-family: 'Cairo', 'Tajawal', sans-serif;
  border-radius: 25px;
  padding: 10px 25px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(45deg, #3bc8e7, #2ba8c7);
  border: none;
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(59, 200, 231, 0.4);
}

/* البطاقات */
.card {
  background-color: #1b2039;
  border: none;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(59, 200, 231, 0.3);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
  .main-content {
    margin-right: 0;
    margin-left: 0;
  }
  
  .page-content {
    padding: 15px;
    margin-top: 60px;
  }
}

/* تحسينات لشريط التمرير */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1b2039;
}

::-webkit-scrollbar-thumb {
  background: #3bc8e7;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #2ba8c7;
}

/* أنيميشن للعناصر */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* تحسينات للروابط */
a {
  color: #3bc8e7;
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: #ffffff;
  text-decoration: none;
}

/* تحسينات للنماذج */
.form-control {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border-radius: 8px;
  font-family: 'Cairo', 'Tajawal', sans-serif;
}

.form-control:focus {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: #3bc8e7;
  box-shadow: 0 0 0 0.2rem rgba(59, 200, 231, 0.25);
  color: #ffffff;
}

.form-control::placeholder {
  color: #777777;
}

/* تحسينات للقوائم */
.list-group-item {
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #777777;
}

.list-group-item:hover {
  background-color: rgba(59, 200, 231, 0.1);
  color: #3bc8e7;
}

/* تحسينات للجداول */
.table {
  color: #777777;
}

.table th {
  border-top: none;
  border-bottom: 2px solid #3bc8e7;
  color: #ffffff;
  font-weight: 600;
}

.table td {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* تحسينات للتنبيهات */
.alert {
  border: none;
  border-radius: 10px;
  font-family: 'Cairo', 'Tajawal', sans-serif;
}

.alert-info {
  background-color: rgba(59, 200, 231, 0.1);
  color: #3bc8e7;
  border: 1px solid rgba(59, 200, 231, 0.3);
}
