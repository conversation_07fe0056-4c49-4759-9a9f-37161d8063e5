import React, { useState } from 'react';
import './App.css';

// استيراد المكونات
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import Home from './pages/Home';
import Albums from './pages/Albums';
import Artists from './pages/Artists';
import Genres from './pages/Genres';

function App() {
  const [currentPage, setCurrentPage] = useState('home');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const renderPage = () => {
    switch(currentPage) {
      case 'home':
        return <Home />;
      case 'albums':
        return <Albums />;
      case 'artists':
        return <Artists />;
      case 'genres':
        return <Genres />;
      default:
        return <Home />;
    }
  };

  return (
    <div className="App" dir="rtl">
      {/* الشريط الجانبي */}
      <Sidebar 
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        onNavigate={setCurrentPage}
        currentPage={currentPage}
      />

      {/* المحتوى الرئيسي */}
      <div className="main-content">
        {/* الهيدر */}
        <Header 
          onMenuClick={() => setSidebarOpen(true)}
        />

        {/* محتوى الصفحة */}
        <div className="page-content">
          {renderPage()}
        </div>
      </div>
    </div>
  );
}

export default App;
