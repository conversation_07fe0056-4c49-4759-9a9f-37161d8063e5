/* مشغل الموسيقى */
.music_player {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #1b2039;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  border-top: 1px solid rgba(59, 200, 231, 0.3);
}

.player_left {
  flex: 1;
  display: flex;
  align-items: center;
  max-width: 300px;
}

.current_track {
  display: flex;
  align-items: center;
  gap: 15px;
}

.track_thumb {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  object-fit: cover;
}

.track_info h4 {
  color: #ffffff;
  font-size: 14px;
  margin: 0 0 5px 0;
  font-weight: 500;
}

.track_info p {
  color: #777777;
  font-size: 12px;
  margin: 0;
}

.player_center {
  flex: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  max-width: 500px;
}

.player_controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.control_btn,
.play_btn {
  background: transparent;
  border: none;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control_btn {
  width: 35px;
  height: 35px;
  font-size: 16px;
}

.play_btn {
  width: 45px;
  height: 45px;
  background: linear-gradient(45deg, #3bc8e7, #2ba8c7);
  border-radius: 50%;
  font-size: 18px;
}

.control_btn:hover {
  color: #3bc8e7;
  transform: scale(1.1);
}

.play_btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(59, 200, 231, 0.4);
}

.progress_bar {
  display: flex;
  align-items: center;
  gap: 15px;
  width: 100%;
}

.time_current,
.time_total {
  color: #777777;
  font-size: 12px;
  min-width: 35px;
  text-align: center;
}

.progress_track {
  flex: 1;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  position: relative;
  cursor: pointer;
}

.progress_fill {
  height: 100%;
  background: linear-gradient(45deg, #3bc8e7, #2ba8c7);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.player_right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 20px;
  max-width: 300px;
}

.volume_control {
  display: flex;
  align-items: center;
  gap: 10px;
}

.volume_control i {
  color: #777777;
  font-size: 14px;
}

.volume_slider {
  width: 80px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
}

.volume_slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 12px;
  height: 12px;
  background: #3bc8e7;
  border-radius: 50%;
  cursor: pointer;
}

.volume_slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: #3bc8e7;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.player_actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.action_btn {
  background: transparent;
  border: none;
  color: #777777;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.action_btn:hover {
  color: #3bc8e7;
  transform: scale(1.1);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
  .music_player {
    padding: 10px 15px;
    flex-direction: column;
    gap: 10px;
  }

  .player_left,
  .player_center,
  .player_right {
    width: 100%;
    max-width: none;
  }

  .player_right {
    justify-content: center;
  }

  .volume_control {
    display: none;
  }

  .current_track {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .progress_bar {
    gap: 10px;
  }

  .volume_slider {
    width: 60px;
  }

  .player_controls {
    gap: 10px;
  }

  .control_btn {
    width: 30px;
    height: 30px;
    font-size: 14px;
  }

  .play_btn {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
}
