/* الموسيقى الحديثة */
.recent_music_wrapper {
  margin: 50px 0;
  padding: 40px;
  background-color: #14182a;
  border-radius: 20px;
}

.recent_music_inner {
  max-width: 1200px;
  margin: 0 auto;
}

.recent_header {
  text-align: center;
  margin-bottom: 40px;
}

.recent_header h1 {
  font-size: 32px;
  color: #3bc8e7;
  font-weight: 700;
  margin: 0;
  text-transform: uppercase;
}

.recent_slider {
  position: relative;
}

.recent_tracks {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 25px;
  transition: all 0.5s ease;
}

.recent_track_card {
  background-color: #1b2039;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.recent_track_card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(59, 200, 231, 0.3);
}

.track_image {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.track_image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.recent_track_card:hover .track_image img {
  transform: scale(1.1);
}

.track_overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.recent_track_card:hover .track_overlay {
  opacity: 1;
}

.overlay_bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
}

.more_icon {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 35px;
  height: 35px;
  background: rgba(59, 200, 231, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.more_icon:hover {
  background: #3bc8e7;
  transform: scale(1.1);
}

.more_icon img {
  width: 16px;
  height: 16px;
  filter: brightness(0) invert(1);
}

.play_icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #3bc8e7, #2ba8c7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.play_icon:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(59, 200, 231, 0.5);
}

.play_icon img {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
  margin-right: 3px;
}

.more_option {
  position: absolute;
  top: 60px;
  right: 15px;
  background: #1b2039;
  border: 1px solid rgba(59, 200, 231, 0.3);
  border-radius: 10px;
  padding: 10px 0;
  list-style: none;
  margin: 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 100;
  min-width: 200px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.more_icon:hover + .more_option,
.more_option:hover {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.more_option li {
  margin: 0;
}

.more_option li a {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 20px;
  color: #777777;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
}

.more_option li a:hover {
  background-color: rgba(59, 200, 231, 0.1);
  color: #3bc8e7;
}

.opt_icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.track_info {
  padding: 20px;
  text-align: center;
}

.track_info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.track_info h3 a {
  color: #ffffff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.track_info h3 a:hover {
  color: #3bc8e7;
}

.track_info p {
  margin: 0;
  font-size: 14px;
  color: #777777;
}

/* أزرار التنقل */
.slider_navigation {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  pointer-events: none;
  z-index: 10;
}

.nav_btn {
  width: 50px;
  height: 50px;
  background: linear-gradient(45deg, #3bc8e7, #2ba8c7);
  border: none;
  border-radius: 50%;
  color: #ffffff;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  pointer-events: all;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav_btn:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(59, 200, 231, 0.5);
}

.prev_btn {
  margin-right: -25px;
}

.next_btn {
  margin-left: -25px;
}

/* مؤشرات الشرائح */
.slider_dots {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 30px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background: #3bc8e7;
  transform: scale(1.2);
}

.dot:hover {
  background: #3bc8e7;
}

/* التصميم المتجاوب */
@media (max-width: 1024px) {
  .recent_tracks {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .recent_music_wrapper {
    padding: 20px;
    margin: 30px 0;
  }
  
  .recent_tracks {
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
  }
  
  .recent_header h1 {
    font-size: 24px;
  }
  
  .track_image {
    height: 150px;
  }
  
  .play_icon {
    width: 50px;
    height: 50px;
  }
  
  .play_icon img {
    width: 20px;
    height: 20px;
  }
  
  .nav_btn {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .recent_tracks {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
  
  .track_image {
    height: 120px;
  }
  
  .track_info {
    padding: 15px;
  }
  
  .track_info h3 {
    font-size: 14px;
  }
  
  .track_info p {
    font-size: 12px;
  }
  
  .more_option {
    min-width: 180px;
  }
}
