/* الشريط الجانبي */
.sidebar_wrapper {
  position: fixed;
  width: 80px;
  background-color: #1b2039;
  height: 100%;
  z-index: 999;
  top: 0;
  bottom: 0;
  right: 0;
  box-shadow: 0px 0px 24px 6px rgba(4, 4, 4, 0.2);
  transition: width 0.3s ease;
}

.sidebar_wrapper:hover,
.sidebar_wrapper.open {
  width: 240px;
}

.sidebar_inner {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 0;
}

.sidebar_close {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background-color: #3bc8e7;
  border-radius: 50%;
  display: none;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
}

.sidebar_logo {
  text-align: center;
  margin-bottom: 30px;
  padding: 0 20px;
}

.sidebar_logo_img {
  max-width: 50px;
  height: auto;
  display: block;
  margin: 0 auto;
}

.sidebar_open_logo_img {
  max-width: 150px;
  height: auto;
  display: none;
  margin: 0 auto;
}

.sidebar_wrapper:hover .sidebar_logo_img,
.sidebar_wrapper.open .sidebar_logo_img {
  display: none;
}

.sidebar_wrapper:hover .sidebar_open_logo_img,
.sidebar_wrapper.open .sidebar_open_logo_img {
  display: block;
}

.nav_sidebar {
  list-style: none;
  padding: 0;
  margin: 0 0 30px 0;
}

.nav_downloads,
.nav_playlist,
.nav_others {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 20px;
}

.nav_sidebar li {
  margin-bottom: 5px;
}

.nav_sidebar a {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #777777;
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 0;
  position: relative;
}

.nav_sidebar a:hover {
  background-color: rgba(59, 200, 231, 0.1);
  color: #3bc8e7;
  transform: translateX(-2px);
}

.nav_sidebar a.active {
  background-color: #3bc8e7;
  color: #ffffff;
}

.nav_sidebar a.active::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background-color: #ffffff;
}

.icon {
  width: 24px;
  height: 24px;
  margin-left: 15px;
  flex-shrink: 0;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.8;
}

.nav_text {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  transform: translateX(20px);
  transition: all 0.3s ease;
}

.sidebar_wrapper:hover .nav_text,
.sidebar_wrapper.open .nav_text {
  opacity: 1;
  transform: translateX(0);
}

/* أيقونات Font Awesome بدلاً من SVG */
.icon_discover::before { content: "\f002"; font-family: FontAwesome; }
.icon_albums::before { content: "\f001"; font-family: FontAwesome; }
.icon_artists::before { content: "\f007"; font-family: FontAwesome; }
.icon_genres::before { content: "\f0c0"; font-family: FontAwesome; }
.icon_tracks::before { content: "\f04b"; font-family: FontAwesome; }
.icon_music::before { content: "\f001"; font-family: FontAwesome; }
.icon_station::before { content: "\f130"; font-family: FontAwesome; }
.icon_download::before { content: "\f019"; font-family: FontAwesome; }
.icon_purchased::before { content: "\f07a"; font-family: FontAwesome; }
.icon_favourite::before { content: "\f004"; font-family: FontAwesome; }
.icon_history::before { content: "\f1da"; font-family: FontAwesome; }
.icon_fe_playlist::before { content: "\f03a"; font-family: FontAwesome; }
.icon_c_playlist::before { content: "\f067"; font-family: FontAwesome; }
.icon_profile::before { content: "\f2bd"; font-family: FontAwesome; }
.icon_upload::before { content: "\f093"; font-family: FontAwesome; }
.icon_blog::before { content: "\f15c"; font-family: FontAwesome; }

/* التصميم المتجاوب */
@media (max-width: 768px) {
  .sidebar_wrapper {
    transform: translateX(100%);
    width: 240px;
  }
  
  .sidebar_wrapper.open {
    transform: translateX(0);
  }

  .sidebar_close {
    display: flex;
  }

  .nav_text {
    opacity: 1;
    transform: translateX(0);
  }
}

.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
}
