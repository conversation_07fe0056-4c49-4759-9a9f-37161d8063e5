import React, { useState } from 'react';

const Header = ({ onMenuClick }) => {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e) => {
    e.preventDefault();
    console.log('البحث عن:', searchQuery);
    // هنا يمكن إضافة منطق البحث
  };

  return (
    <div className="header_wrapper">
      <div className="header_inner">
        {/* الجانب الأيسر - البحث والترند */}
        <div className="header_left">
          {/* شريط البحث */}
          <div className="search_wrapper">
            <form onSubmit={handleSearch}>
              <input
                type="text"
                placeholder="ابحث عن الموسيقى هنا.."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="search_input"
              />
              <button type="submit" className="search_btn">
                <img src="/images/svg/search.svg" alt="بحث" />
              </button>
            </form>
          </div>

          {/* الأغاني الرائجة */}
          <div className="trending_wrapper">
            <span className="trending_label">
              <a href="#">الأغاني الرائجة :</a>
            </span>
            <div className="trending_marquee">
              <a href="#">
                احلم بلحظاتك، حتى التقيت بك، امنحني بعض الشجاعة، الزقاق المظلم (+8 أخرى)
              </a>
            </div>
          </div>
        </div>

        {/* الجانب الأيمن - اللغات والأزرار */}
        <div className="header_right">
          {/* اللغات */}
          <div className="language_wrapper">
            <span>
              اللغات <img src="/images/svg/lang.svg" alt="اللغات" />
            </span>
          </div>

          {/* أزرار التسجيل */}
          <div className="auth_buttons">
            <button className="btn_register">
              <span>تسجيل</span>
            </button>
            <button className="btn_login">
              <span>دخول</span>
            </button>
          </div>

          {/* زر القائمة للموبايل */}
          <button className="mobile_menu_btn" onClick={onMenuClick}>
            <i className="fa fa-bars"></i>
          </button>
        </div>
      </div>

      <style>{`
        .header_wrapper {
          position: fixed;
          background-color: #1b2039;
          padding: 20px 50px;
          left: 0;
          right: 80px;
          top: 0;
          z-index: 998;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
          backdrop-filter: blur(10px);
        }

        .header_inner {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 30px;
        }

        .header_left {
          display: flex;
          align-items: center;
          gap: 30px;
          flex: 1;
        }

        .header_right {
          display: flex;
          align-items: center;
          gap: 20px;
        }

        /* شريط البحث */
        .search_wrapper {
          max-width: 395px;
          position: relative;
          flex: 1;
        }

        .search_wrapper form {
          position: relative;
          display: flex;
        }

        .search_input {
          width: 100%;
          height: 40px;
          background-color: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 5px 0 0 5px;
          padding: 0 60px 0 15px;
          color: #ffffff;
          font-family: 'Cairo', 'Tajawal', sans-serif;
          font-size: 14px;
          outline: none;
          transition: all 0.3s ease;
        }

        .search_input::placeholder {
          color: #777777;
        }

        .search_input:focus {
          border-color: #3bc8e7;
          background-color: rgba(255, 255, 255, 0.15);
        }

        .search_btn {
          position: absolute;
          left: 0;
          top: 0;
          height: 40px;
          background-color: #3bc8e7;
          border: none;
          border-radius: 0 5px 5px 0;
          padding: 0 16px;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .search_btn:hover {
          background-color: #2ba8c7;
        }

        .search_btn img {
          width: 18px;
          height: 18px;
          filter: brightness(0) invert(1);
        }

        /* الأغاني الرائجة */
        .trending_wrapper {
          display: flex;
          align-items: center;
          gap: 15px;
          flex: 1;
          min-width: 0;
        }

        .trending_label {
          white-space: nowrap;
        }

        .trending_label a {
          color: #3bc8e7;
          font-weight: 500;
          text-decoration: none;
          font-size: 14px;
        }

        .trending_label a:hover {
          color: #ffffff;
        }

        .trending_marquee {
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
        }

        .trending_marquee a {
          display: inline-block;
          color: #777777;
          text-decoration: none;
          font-size: 14px;
          animation: marquee 20s linear infinite;
          padding-right: 100%;
        }

        .trending_marquee a:hover {
          color: #3bc8e7;
          animation-play-state: paused;
        }

        @keyframes marquee {
          0% { transform: translateX(100%); }
          100% { transform: translateX(-100%); }
        }

        /* اللغات */
        .language_wrapper {
          cursor: pointer;
          color: #777777;
          font-size: 14px;
          transition: color 0.3s ease;
          display: flex;
          align-items: center;
          gap: 8px;
          white-space: nowrap;
        }

        .language_wrapper:hover {
          color: #3bc8e7;
        }

        .language_wrapper img {
          width: 16px;
          height: 16px;
        }

        /* أزرار التسجيل */
        .auth_buttons {
          display: flex;
          align-items: center;
          gap: 15px;
        }

        .btn_register,
        .btn_login {
          background: linear-gradient(45deg, #3bc8e7, #2ba8c7);
          color: #ffffff;
          border: none;
          border-radius: 20px;
          padding: 8px 20px;
          font-family: 'Cairo', 'Tajawal', sans-serif;
          font-size: 12px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
          min-width: 80px;
        }

        .btn_register {
          background: transparent;
          border: 2px solid #3bc8e7;
          color: #3bc8e7;
        }

        .btn_register:hover {
          background: #3bc8e7;
          color: #ffffff;
        }

        .btn_login:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 15px rgba(59, 200, 231, 0.4);
        }

        /* زر القائمة للموبايل */
        .mobile_menu_btn {
          display: none;
          background: #3bc8e7;
          border: none;
          border-radius: 8px;
          padding: 10px;
          color: #ffffff;
          cursor: pointer;
          font-size: 16px;
        }

        /* التصميم المتجاوب */
        @media (max-width: 768px) {
          .header_wrapper {
            right: 0;
            padding: 15px 20px;
          }

          .header_inner {
            flex-direction: column;
            gap: 15px;
          }

          .header_left {
            width: 100%;
            flex-direction: column;
            gap: 15px;
          }

          .header_right {
            width: 100%;
            justify-content: center;
          }

          .search_wrapper {
            max-width: 100%;
          }

          .trending_wrapper {
            width: 100%;
          }

          .mobile_menu_btn {
            display: block;
          }

          .auth_buttons {
            order: -1;
          }
        }

        @media (max-width: 480px) {
          .auth_buttons {
            flex-direction: column;
            gap: 10px;
          }

          .btn_register,
          .btn_login {
            width: 100%;
            min-width: auto;
          }
        }
      `}</style>
    </div>
  );
};

export default Header;
