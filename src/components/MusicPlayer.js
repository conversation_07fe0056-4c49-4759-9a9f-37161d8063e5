import React, { useState } from 'react';
import './MusicPlayer.css';

const MusicPlayer = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(180); // 3 minutes
  const [volume, setVolume] = useState(50);

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="music_player">
      <div className="player_left">
        <div className="current_track">
          <img src="/images/music/r_music1.jpg" alt="Current Track" className="track_thumb" />
          <div className="track_info">
            <h4>احلم بلحظاتك</h4>
            <p>آفا كورنيش وبرايان هيل</p>
          </div>
        </div>
      </div>

      <div className="player_center">
        <div className="player_controls">
          <button className="control_btn">
            <i className="fa fa-step-backward"></i>
          </button>
          <button className="play_btn" onClick={togglePlay}>
            <i className={`fa fa-${isPlaying ? 'pause' : 'play'}`}></i>
          </button>
          <button className="control_btn">
            <i className="fa fa-step-forward"></i>
          </button>
        </div>
        <div className="progress_bar">
          <span className="time_current">{formatTime(currentTime)}</span>
          <div className="progress_track">
            <div 
              className="progress_fill"
              style={{ width: `${(currentTime / duration) * 100}%` }}
            ></div>
          </div>
          <span className="time_total">{formatTime(duration)}</span>
        </div>
      </div>

      <div className="player_right">
        <div className="volume_control">
          <i className="fa fa-volume-up"></i>
          <input 
            type="range" 
            min="0" 
            max="100" 
            value={volume}
            onChange={(e) => setVolume(e.target.value)}
            className="volume_slider"
          />
        </div>
        <div className="player_actions">
          <button className="action_btn">
            <i className="fa fa-heart-o"></i>
          </button>
          <button className="action_btn">
            <i className="fa fa-list"></i>
          </button>
        </div>
      </div>
    </div>
  );
};

export default MusicPlayer;
