/* أفضل 15 أغنية هذا الأسبوع */
.weekly_wrapper {
  margin: 50px 0;
  padding: 40px;
  background-color: #1b2039;
  border-radius: 20px;
}

.weekly_inner {
  max-width: 1200px;
  margin: 0 auto;
}

.weekly_header {
  text-align: center;
  margin-bottom: 40px;
}

.weekly_header h1 {
  font-size: 32px;
  color: #3bc8e7;
  font-weight: 700;
  margin: 0;
  text-transform: uppercase;
}

.weekly_content {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
}

.weekly_column {
  display: flex;
  flex-direction: column;
}

.weekly_box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 0;
  position: relative;
  transition: all 0.3s ease;
  border-radius: 10px;
}

.weekly_box:hover {
  background-color: rgba(59, 200, 231, 0.1);
  transform: translateX(-5px);
}

.weekly_box.active_play {
  background-color: rgba(59, 200, 231, 0.2);
}

.weekly_left {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.top_number {
  font-size: 24px;
  font-weight: 700;
  color: #3bc8e7;
  min-width: 30px;
  text-align: center;
}

.top_song {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.song_image {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
}

.song_image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.song_overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.song_image:hover .song_overlay {
  opacity: 1;
}

.play_icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  background: rgba(59, 200, 231, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
  cursor: pointer;
}

.song_image:hover .play_icon {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

.weekly_box.active_play .play_icon {
  opacity: 1;
  background: transparent;
}

.play_icon img {
  width: 12px;
  height: 12px;
  filter: brightness(0) invert(1);
}

.music_bars {
  display: flex;
  align-items: center;
  gap: 2px;
  height: 16px;
}

.music_bars .bar {
  width: 2px;
  background: #3bc8e7;
  border-radius: 1px;
  animation: musicBars 1s ease-in-out infinite;
}

.music_bars .bar:nth-child(1) { animation-delay: 0s; height: 8px; }
.music_bars .bar:nth-child(2) { animation-delay: 0.1s; height: 12px; }
.music_bars .bar:nth-child(3) { animation-delay: 0.2s; height: 16px; }
.music_bars .bar:nth-child(4) { animation-delay: 0.3s; height: 10px; }
.music_bars .bar:nth-child(5) { animation-delay: 0.4s; height: 14px; }
.music_bars .bar:nth-child(6) { animation-delay: 0.5s; height: 6px; }

@keyframes musicBars {
  0%, 100% { transform: scaleY(1); }
  50% { transform: scaleY(0.3); }
}

.song_name {
  flex: 1;
}

.song_name h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: 600;
}

.song_name h3 a {
  color: #ffffff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.song_name h3 a:hover {
  color: #3bc8e7;
}

.song_name p {
  margin: 0;
  font-size: 14px;
  color: #777777;
}

.weekly_right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.song_time {
  font-size: 14px;
  color: #777777;
  min-width: 40px;
  text-align: center;
}

.more_icon {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.more_icon:hover {
  background-color: rgba(59, 200, 231, 0.2);
}

.more_icon img {
  width: 16px;
  height: 16px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.more_icon:hover img {
  opacity: 1;
}

.more_option {
  position: absolute;
  top: 100%;
  right: 0;
  background: #1b2039;
  border: 1px solid rgba(59, 200, 231, 0.3);
  border-radius: 10px;
  padding: 10px 0;
  list-style: none;
  margin: 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 100;
  min-width: 200px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.weekly_box:hover .more_option {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.more_option li {
  margin: 0;
}

.more_option li a {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 20px;
  color: #777777;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
}

.more_option li a:hover {
  background-color: rgba(59, 200, 231, 0.1);
  color: #3bc8e7;
}

.opt_icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: 10px 0;
}

.weekly_box:last-child .divider {
  display: none;
}

/* التصميم المتجاوب */
@media (max-width: 1024px) {
  .weekly_content {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
  
  .weekly_column:last-child {
    grid-column: 1 / -1;
  }
}

@media (max-width: 768px) {
  .weekly_wrapper {
    padding: 20px;
    margin: 30px 0;
  }
  
  .weekly_content {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .weekly_header h1 {
    font-size: 24px;
  }
  
  .top_number {
    font-size: 20px;
    min-width: 25px;
  }
  
  .song_image {
    width: 50px;
    height: 50px;
  }
  
  .song_name h3 {
    font-size: 14px;
  }
  
  .song_name p {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .weekly_box {
    padding: 10px 0;
  }
  
  .weekly_left {
    gap: 10px;
  }
  
  .top_song {
    gap: 10px;
  }
  
  .song_image {
    width: 40px;
    height: 40px;
  }
  
  .weekly_right {
    gap: 10px;
  }
  
  .more_option {
    min-width: 180px;
  }
}
