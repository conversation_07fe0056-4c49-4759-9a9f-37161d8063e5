import React from 'react';
import './WeeklyTop.css';

const WeeklyTop = () => {
  const weeklyTracks = [
    { id: 1, rank: '01', title: 'حتى التقيت بك', artist: 'آفا كورنيش', image: '/images/weekly/song1.jpg', duration: '5:10' },
    { id: 2, rank: '02', title: 'وعود المشي', artist: 'آفا كورنيش', image: '/images/weekly/song2.jpg', duration: '4:32' },
    { id: 3, rank: '03', title: 'امنحني بعض الشجاعة', artist: 'آفا كورنيش', image: '/images/weekly/song3.jpg', duration: '3:45' },
    { id: 4, rank: '04', title: 'الألعاب المرغوبة', artist: 'آفا كورنيش', image: '/images/weekly/song4.jpg', duration: '4:18' },
    { id: 5, rank: '05', title: 'الزقاق المظلم الصوتي', artist: 'آفا كورنيش', image: '/images/weekly/song5.jpg', duration: '3:55' },
    { id: 6, rank: '06', title: 'وعود المشي', artist: 'آفا كورنيش', image: '/images/weekly/song6.jpg', duration: '4:22' },
    { id: 7, rank: '07', title: 'أشياء لا نهائية', artist: 'آفا كورنيش', image: '/images/weekly/song7.jpg', duration: '3:38' },
    { id: 8, rank: '08', title: 'احلم بلحظاتك', artist: 'آفا كورنيش', image: '/images/weekly/song8.jpg', duration: '4:05', isPlaying: true },
    { id: 9, rank: '09', title: 'توقف نبضات القلب', artist: 'آفا كورنيش', image: '/images/weekly/song9.jpg', duration: '3:28' },
    { id: 10, rank: '10', title: 'واحد أكثر من غريب', artist: 'آفا كورنيش', image: '/images/weekly/song10.jpg', duration: '4:15' },
    { id: 11, rank: '11', title: 'الألعاب المرغوبة', artist: 'آفا كورنيش', image: '/images/weekly/song11.jpg', duration: '3:52' },
    { id: 12, rank: '12', title: 'حتى التقيت بك', artist: 'آفا كورنيش', image: '/images/weekly/song12.jpg', duration: '4:08' },
    { id: 13, rank: '13', title: 'امنحني بعض الشجاعة', artist: 'آفا كورنيش', image: '/images/weekly/song13.jpg', duration: '3:33' },
    { id: 14, rank: '14', title: 'الزقاق المظلم', artist: 'آفا كورنيش', image: '/images/weekly/song14.jpg', duration: '4:42' },
    { id: 15, rank: '15', title: 'وعود المشي', artist: 'آفا كورنيش', image: '/images/weekly/song15.jpg', duration: '3:58' }
  ];

  const handlePlay = (track) => {
    console.log('تشغيل الأغنية:', track.title);
  };

  const handleMoreOptions = (track) => {
    console.log('خيارات إضافية للأغنية:', track.title);
  };

  const renderTrackGroup = (tracks) => {
    return tracks.map((track) => (
      <div key={track.id} className={`weekly_box ${track.isPlaying ? 'active_play' : ''}`}>
        <div className="weekly_left">
          <span className="top_number">{track.rank}</span>
          <div className="top_song">
            <div className="song_image">
              <img src={track.image} alt={track.title} />
              <div className="song_overlay"></div>
              <div className="play_icon" onClick={() => handlePlay(track)}>
                {track.isPlaying ? (
                  <div className="music_bars">
                    <div className="bar"></div>
                    <div className="bar"></div>
                    <div className="bar"></div>
                    <div className="bar"></div>
                    <div className="bar"></div>
                    <div className="bar"></div>
                  </div>
                ) : (
                  <img src="/images/svg/play.svg" alt="تشغيل" />
                )}
              </div>
            </div>
            <div className="song_name">
              <h3><a href="#">{track.title}</a></h3>
              <p>{track.artist}</p>
            </div>
          </div>
        </div>
        <div className="weekly_right">
          <span className="song_time">{track.duration}</span>
          <span className="more_icon" onClick={() => handleMoreOptions(track)}>
            <img src="/images/svg/more.svg" alt="المزيد" />
          </span>
        </div>
        <ul className="more_option">
          <li><a href="#"><span className="opt_icon"><span className="icon icon_fav"></span></span>إضافة للمفضلة</a></li>
          <li><a href="#"><span className="opt_icon"><span className="icon icon_queue"></span></span>إضافة للقائمة</a></li>
          <li><a href="#"><span className="opt_icon"><span className="icon icon_dwn"></span></span>تحميل الآن</a></li>
          <li><a href="#"><span className="opt_icon"><span className="icon icon_playlst"></span></span>إضافة لقائمة التشغيل</a></li>
          <li><a href="#"><span className="opt_icon"><span className="icon icon_share"></span></span>مشاركة</a></li>
        </ul>
        <div className="divider"></div>
      </div>
    ));
  };

  return (
    <div className="weekly_wrapper">
      <div className="weekly_inner">
        <div className="weekly_header">
          <h1>أفضل 15 أغنية هذا الأسبوع</h1>
        </div>
        <div className="weekly_content">
          <div className="weekly_column">
            {renderTrackGroup(weeklyTracks.slice(0, 5))}
          </div>
          <div className="weekly_column">
            {renderTrackGroup(weeklyTracks.slice(5, 10))}
          </div>
          <div className="weekly_column">
            {renderTrackGroup(weeklyTracks.slice(10, 15))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default WeeklyTop;
