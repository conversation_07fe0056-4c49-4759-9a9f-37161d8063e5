import React from 'react';
import './Sidebar.css';

const Sidebar = ({ isOpen, onClose, onNavigate, currentPage }) => {
  const menuItems = [
    { id: 'home', icon: 'discover', text: 'اكتشف', title: 'الصفحة الرئيسية' },
    { id: 'albums', icon: 'albums', text: 'الألبومات', title: 'الألبومات' },
    { id: 'artists', icon: 'artists', text: 'الفنانين', title: 'الفنانين' },
    { id: 'genres', icon: 'genres', text: 'الأنواع', title: 'الأنواع الموسيقية' },
    { id: 'top-tracks', icon: 'tracks', text: 'أفضل الأغاني', title: 'أفضل الأغاني' },
    { id: 'free-music', icon: 'music', text: 'موسيقى مجانية', title: 'موسيقى مجانية' },
    { id: 'stations', icon: 'station', text: 'المحطات', title: 'المحطات الإذاعية' }
  ];

  const downloadItems = [
    { id: 'downloads', icon: 'download', text: 'التحميلات', title: 'التحميلات' },
    { id: 'purchases', icon: 'purchased', text: 'المشتريات', title: 'المشتريات' },
    { id: 'favourites', icon: 'favourite', text: 'المفضلة', title: 'الأغاني المفضلة' },
    { id: 'history', icon: 'history', text: 'التاريخ', title: 'تاريخ الاستماع' }
  ];

  const playlistItems = [
    { id: 'featured-playlists', icon: 'fe_playlist', text: 'قوائم مميزة', title: 'قوائم التشغيل المميزة' },
    { id: 'create-playlist', icon: 'c_playlist', text: 'إنشاء قائمة', title: 'إنشاء قائمة تشغيل جديدة' }
  ];

  const otherItems = [
    { id: 'profile', icon: 'profile', text: 'الملف الشخصي', title: 'الملف الشخصي' },
    { id: 'upload', icon: 'upload', text: 'رفع الملفات', title: 'رفع الملفات الصوتية' },
    { id: 'blog', icon: 'blog', text: 'المدونة', title: 'مدونة الموسيقى' }
  ];

  const handleItemClick = (pageId) => {
    onNavigate(pageId);
    onClose();
  };

  const renderMenuSection = (items, className = '') => (
    <ul className={`nav_sidebar ${className}`}>
      {items.map((item) => (
        <li key={item.id}>
          <a
            href="#"
            className={currentPage === item.id ? 'active' : ''}
            title={item.title}
            onClick={(e) => {
              e.preventDefault();
              handleItemClick(item.id);
            }}
          >
            <span className={`icon icon_${item.icon}`}></span>
            <span className="nav_text">{item.text}</span>
          </a>
        </li>
      ))}
    </ul>
  );

  return (
    <>
      {/* Overlay للموبايل */}
      {isOpen && window.innerWidth <= 768 && (
        <div
          className="sidebar-overlay"
          onClick={onClose}
        />
      )}

      {/* الشريط الجانبي */}
      <div className={`sidebar_wrapper ${isOpen ? 'open' : ''}`}>
        <div className="sidebar_inner">
          {/* زر الإغلاق للموبايل */}
          <div className="sidebar_close" onClick={onClose}>
            <i className="fa fa-angle-left" aria-hidden="true"></i>
          </div>

          {/* الشعار */}
          <div className="sidebar_logo">
            <a href="#" onClick={(e) => { e.preventDefault(); handleItemClick('home'); }}>
              <img src="/images/logo.png" alt="معجزة" className="sidebar_logo_img" />
            </a>
            <a href="#" onClick={(e) => { e.preventDefault(); handleItemClick('home'); }}>
              <img src="/images/open_logo.png" alt="معجزة" className="sidebar_open_logo_img" />
            </a>
          </div>

          {/* القوائم */}
          <div className="sidebar_nav">
            {/* القائمة الرئيسية */}
            {renderMenuSection(menuItems)}
            
            {/* قائمة التحميلات */}
            {renderMenuSection(downloadItems, 'nav_downloads')}
            
            {/* قوائم التشغيل */}
            {renderMenuSection(playlistItems, 'nav_playlist')}
            
            {/* أخرى */}
            {renderMenuSection(otherItems, 'nav_others')}
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
