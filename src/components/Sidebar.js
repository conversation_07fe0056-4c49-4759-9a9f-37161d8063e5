import React from 'react';

const Sidebar = ({ isOpen, onClose, onNavigate, currentPage }) => {
  const menuItems = [
    { id: 'home', icon: 'discover', text: 'اكتشف', title: 'الصفحة الرئيسية' },
    { id: 'albums', icon: 'albums', text: 'الألبومات', title: 'الألبومات' },
    { id: 'artists', icon: 'artists', text: 'الفنانين', title: 'الفنانين' },
    { id: 'genres', icon: 'genres', text: 'الأنواع', title: 'الأنواع الموسيقية' },
    { id: 'top-tracks', icon: 'tracks', text: 'أفضل الأغاني', title: 'أفضل الأغاني' },
    { id: 'free-music', icon: 'music', text: 'موسيقى مجانية', title: 'موسيقى مجانية' },
    { id: 'stations', icon: 'station', text: 'المحطات', title: 'المحطات الإذاعية' }
  ];

  const downloadItems = [
    { id: 'downloads', icon: 'download', text: 'التحميلات', title: 'التحميلات' },
    { id: 'purchases', icon: 'purchased', text: 'المشتريات', title: 'المشتريات' },
    { id: 'favourites', icon: 'favourite', text: 'المفضلة', title: 'الأغاني المفضلة' },
    { id: 'history', icon: 'history', text: 'التاريخ', title: 'تاريخ الاستماع' }
  ];

  const playlistItems = [
    { id: 'featured-playlists', icon: 'fe_playlist', text: 'قوائم مميزة', title: 'قوائم التشغيل المميزة' },
    { id: 'create-playlist', icon: 'c_playlist', text: 'إنشاء قائمة', title: 'إنشاء قائمة تشغيل جديدة' }
  ];

  const otherItems = [
    { id: 'profile', icon: 'profile', text: 'الملف الشخصي', title: 'الملف الشخصي' },
    { id: 'upload', icon: 'upload', text: 'رفع الملفات', title: 'رفع الملفات الصوتية' },
    { id: 'blog', icon: 'blog', text: 'المدونة', title: 'مدونة الموسيقى' }
  ];

  const handleItemClick = (pageId) => {
    onNavigate(pageId);
    onClose();
  };

  const renderMenuSection = (items, className = '') => (
    <ul className={`nav_sidebar ${className}`}>
      {items.map((item) => (
        <li key={item.id}>
          <a
            href="#"
            className={currentPage === item.id ? 'active' : ''}
            title={item.title}
            onClick={(e) => {
              e.preventDefault();
              handleItemClick(item.id);
            }}
          >
            <span className={`icon icon_${item.icon}`}></span>
            <span className="nav_text">{item.text}</span>
          </a>
        </li>
      ))}
    </ul>
  );

  return (
    <>
      {/* Overlay للموبايل */}
      {isOpen && (
        <div 
          className="sidebar-overlay"
          onClick={onClose}
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 998,
            display: window.innerWidth <= 768 ? 'block' : 'none'
          }}
        />
      )}

      {/* الشريط الجانبي */}
      <div className={`sidebar_wrapper ${isOpen ? 'open' : ''}`}>
        <div className="sidebar_inner">
          {/* زر الإغلاق للموبايل */}
          <div className="sidebar_close" onClick={onClose}>
            <i className="fa fa-angle-left" aria-hidden="true"></i>
          </div>

          {/* الشعار */}
          <div className="sidebar_logo">
            <a href="#" onClick={(e) => { e.preventDefault(); handleItemClick('home'); }}>
              <img src="/images/logo.png" alt="معجزة" className="sidebar_logo_img" />
            </a>
            <a href="#" onClick={(e) => { e.preventDefault(); handleItemClick('home'); }}>
              <img src="/images/open_logo.png" alt="معجزة" className="sidebar_open_logo_img" />
            </a>
          </div>

          {/* القوائم */}
          <div className="sidebar_nav">
            {/* القائمة الرئيسية */}
            {renderMenuSection(menuItems)}
            
            {/* قائمة التحميلات */}
            {renderMenuSection(downloadItems, 'nav_downloads')}
            
            {/* قوائم التشغيل */}
            {renderMenuSection(playlistItems, 'nav_playlist')}
            
            {/* أخرى */}
            {renderMenuSection(otherItems, 'nav_others')}
          </div>
        </div>
      </div>

      <style>{`
        .sidebar_wrapper {
          position: fixed;
          width: 80px;
          background-color: #1b2039;
          height: 100%;
          z-index: 999;
          top: 0;
          bottom: 0;
          right: 0;
          box-shadow: 0px 0px 24px 6px rgba(4, 4, 4, 0.2);
          transition: width 0.3s ease;
        }

        .sidebar_wrapper:hover,
        .sidebar_wrapper.open {
          width: 240px;
        }

        .sidebar_inner {
          height: 100%;
          overflow-y: auto;
          overflow-x: hidden;
          padding: 20px 0;
        }

        .sidebar_close {
          position: absolute;
          top: 20px;
          right: 20px;
          width: 40px;
          height: 40px;
          background-color: #3bc8e7;
          border-radius: 50%;
          display: none;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          z-index: 10;
        }

        .sidebar_logo {
          text-align: center;
          margin-bottom: 30px;
          padding: 0 20px;
        }

        .sidebar_logo_img {
          max-width: 50px;
          height: auto;
          display: block;
          margin: 0 auto;
        }

        .sidebar_open_logo_img {
          max-width: 150px;
          height: auto;
          display: none;
          margin: 0 auto;
        }

        .sidebar_wrapper:hover .sidebar_logo_img,
        .sidebar_wrapper.open .sidebar_logo_img {
          display: none;
        }

        .sidebar_wrapper:hover .sidebar_open_logo_img,
        .sidebar_wrapper.open .sidebar_open_logo_img {
          display: block;
        }

        .nav_sidebar {
          list-style: none;
          padding: 0;
          margin: 0 0 30px 0;
        }

        .nav_downloads,
        .nav_playlist,
        .nav_others {
          border-top: 1px solid rgba(255, 255, 255, 0.1);
          padding-top: 20px;
        }

        .nav_sidebar li {
          margin-bottom: 5px;
        }

        .nav_sidebar a {
          display: flex;
          align-items: center;
          padding: 12px 20px;
          color: #777777;
          text-decoration: none;
          transition: all 0.3s ease;
          border-radius: 0;
          position: relative;
        }

        .nav_sidebar a:hover {
          background-color: rgba(59, 200, 231, 0.1);
          color: #3bc8e7;
          transform: translateX(-2px);
        }

        .nav_sidebar a.active {
          background-color: #3bc8e7;
          color: #ffffff;
        }

        .nav_sidebar a.active::before {
          content: '';
          position: absolute;
          top: 0;
          right: 0;
          width: 4px;
          height: 100%;
          background-color: #ffffff;
        }

        .icon {
          width: 24px;
          height: 24px;
          margin-left: 15px;
          flex-shrink: 0;
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          opacity: 0.8;
        }

        .nav_text {
          font-size: 14px;
          font-weight: 500;
          white-space: nowrap;
          opacity: 0;
          transform: translateX(20px);
          transition: all 0.3s ease;
        }

        .sidebar_wrapper:hover .nav_text,
        .sidebar_wrapper.open .nav_text {
          opacity: 1;
          transform: translateX(0);
        }

        /* أيقونات SVG */
        .icon_discover { background-image: url('/images/svg/discover.svg'); }
        .icon_albums { background-image: url('/images/svg/albums.svg'); }
        .icon_artists { background-image: url('/images/svg/artists.svg'); }
        .icon_genres { background-image: url('/images/svg/genres.svg'); }
        .icon_tracks { background-image: url('/images/svg/tracks.svg'); }
        .icon_music { background-image: url('/images/svg/music.svg'); }
        .icon_station { background-image: url('/images/svg/station.svg'); }
        .icon_download { background-image: url('/images/svg/download.svg'); }
        .icon_purchased { background-image: url('/images/svg/purchased.svg'); }
        .icon_favourite { background-image: url('/images/svg/favourite.svg'); }
        .icon_history { background-image: url('/images/svg/history.svg'); }
        .icon_fe_playlist { background-image: url('/images/svg/playlist.svg'); }
        .icon_c_playlist { background-image: url('/images/svg/create.svg'); }
        .icon_profile { background-image: url('/images/svg/profile.svg'); }
        .icon_upload { background-image: url('/images/svg/upload.svg'); }
        .icon_blog { background-image: url('/images/svg/blog.svg'); }

        /* التصميم المتجاوب */
        @media (max-width: 768px) {
          .sidebar_wrapper {
            transform: translateX(100%);
            width: 240px;
          }
          
          .sidebar_wrapper.open {
            transform: translateX(0);
          }

          .sidebar_close {
            display: flex;
          }

          .nav_text {
            opacity: 1;
            transform: translateX(0);
          }
        }
      `}</style>
    </>
  );
};

export default Sidebar;
