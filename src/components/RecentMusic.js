import React, { useState } from 'react';
import './RecentMusic.css';

const RecentMusic = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const recentTracks = [
    { id: 1, title: 'احلم بلحظاتك (ديو)', artist: 'آفا كورنيش وبرايان هيل', image: '/images/music/r_music1.jpg' },
    { id: 2, title: 'حتى التقيت بك', artist: 'آفا كورنيش وبرايان هيل', image: '/images/music/r_music2.jpg' },
    { id: 3, title: 'امنحني بعض الشجاعة', artist: 'آفا كورنيش وبرايان هيل', image: '/images/music/r_music3.jpg' },
    { id: 4, title: 'الزقاق المظلم الصوتي', artist: 'آفا كورنيش وبرايان هيل', image: '/images/music/r_music4.jpg' },
    { id: 5, title: 'وعود المشي', artist: 'آفا كورنيش وبرايان هيل', image: '/images/music/r_music5.jpg' },
    { id: 6, title: 'الألعاب المرغوبة', artist: 'آفا كورنيش وبرايان هيل', image: '/images/music/r_music6.jpg' },
    { id: 7, title: 'احلم بلحظاتك (ديو)', artist: 'آفا كورنيش وبرايان هيل', image: '/images/music/r_music1.jpg' },
    { id: 8, title: 'حتى التقيت بك', artist: 'آفا كورنيش وبرايان هيل', image: '/images/music/r_music2.jpg' },
    { id: 9, title: 'امنحني بعض الشجاعة', artist: 'آفا كورنيش وبرايان هيل', image: '/images/music/r_music3.jpg' },
    { id: 10, title: 'الزقاق المظلم الصوتي', artist: 'آفا كورنيش وبرايان هيل', image: '/images/music/r_music4.jpg' }
  ];

  const tracksPerSlide = 5;
  const totalSlides = Math.ceil(recentTracks.length / tracksPerSlide);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % totalSlides);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + totalSlides) % totalSlides);
  };

  const handlePlay = (track) => {
    console.log('تشغيل الأغنية:', track.title);
  };

  const handleMoreOptions = (track) => {
    console.log('خيارات إضافية للأغنية:', track.title);
  };

  const getCurrentTracks = () => {
    const startIndex = currentSlide * tracksPerSlide;
    return recentTracks.slice(startIndex, startIndex + tracksPerSlide);
  };

  return (
    <div className="recent_music_wrapper">
      <div className="recent_music_inner">
        <div className="recent_header">
          <h1>الموسيقى الحديثة</h1>
        </div>
        
        <div className="recent_slider">
          <div className="recent_tracks">
            {getCurrentTracks().map((track) => (
              <div key={track.id} className="recent_track_card">
                <div className="track_image">
                  <img src={track.image} alt={track.title} />
                  <div className="track_overlay">
                    <div className="overlay_bg"></div>
                    <div className="more_icon" onClick={() => handleMoreOptions(track)}>
                      <img src="/images/svg/more.svg" alt="المزيد" />
                    </div>
                    <ul className="more_option">
                      <li><a href="#"><span className="opt_icon"><span className="icon icon_fav"></span></span>إضافة للمفضلة</a></li>
                      <li><a href="#"><span className="opt_icon"><span className="icon icon_queue"></span></span>إضافة للقائمة</a></li>
                      <li><a href="#"><span className="opt_icon"><span className="icon icon_dwn"></span></span>تحميل الآن</a></li>
                      <li><a href="#"><span className="opt_icon"><span className="icon icon_playlst"></span></span>إضافة لقائمة التشغيل</a></li>
                      <li><a href="#"><span className="opt_icon"><span className="icon icon_share"></span></span>مشاركة</a></li>
                    </ul>
                    <div className="play_icon" onClick={() => handlePlay(track)}>
                      <img src="/images/svg/play.svg" alt="تشغيل" />
                    </div>
                  </div>
                </div>
                <div className="track_info">
                  <h3><a href="#">{track.title}</a></h3>
                  <p>{track.artist}</p>
                </div>
              </div>
            ))}
          </div>
          
          {/* أزرار التنقل */}
          <div className="slider_navigation">
            <button className="nav_btn prev_btn" onClick={prevSlide}>
              <i className="fa fa-chevron-right"></i>
            </button>
            <button className="nav_btn next_btn" onClick={nextSlide}>
              <i className="fa fa-chevron-left"></i>
            </button>
          </div>
          
          {/* مؤشرات الشرائح */}
          <div className="slider_dots">
            {Array.from({ length: totalSlides }).map((_, index) => (
              <button
                key={index}
                className={`dot ${index === currentSlide ? 'active' : ''}`}
                onClick={() => setCurrentSlide(index)}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecentMusic;
