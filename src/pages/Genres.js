import React from 'react';

const Genres = () => {
  const genres = [
    { id: 1, name: 'بوب عربي', description: 'أحدث أغاني البوب العربي', image: '/images/genrs/genrs1.jpg', tracksCount: 150 },
    { id: 2, name: 'كلاسيكي عربي', description: 'الموسيقى الكلاسيكية العربية الأصيلة', image: '/images/genrs/genrs2.jpg', tracksCount: 89 },
    { id: 3, name: 'راب عربي', description: 'أقوى أغاني الراب العربي', image: '/images/genrs/genrs3.jpg', tracksCount: 67 },
    { id: 4, name: 'روك عربي', description: 'موسيقى الروك بالطابع العربي', image: '/images/genrs/genrs4.jpg', tracksCount: 45 },
    { id: 5, name: 'إلكتروني', description: 'الموسيقى الإلكترونية العربية', image: '/images/genrs/genrs5.jpg', tracksCount: 78 },
    { id: 6, name: 'فولك عربي', description: 'الموسيقى الشعبية العربية', image: '/images/genrs/genrs6.jpg', tracksCount: 92 },
  ];

  return (
    <div className="genres_page">
      <div className="page_header">
        <h1>الأنواع الموسيقية</h1>
        <p>اكتشف أنواع مختلفة من الموسيقى العربية والعالمية</p>
      </div>

      <div className="genres_grid">
        {genres.map((genre) => (
          <div key={genre.id} className="genre_card">
            <div className="genre_image">
              <img src={genre.image} alt={genre.name} />
              <div className="genre_overlay">
                <div className="genre_content">
                  <h3>{genre.name}</h3>
                  <p>{genre.description}</p>
                  <span className="tracks_count">{genre.tracksCount} أغنية</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <style>{`
        .genres_page {
          padding: 20px;
          max-width: 1200px;
          margin: 0 auto;
        }

        .page_header {
          text-align: center;
          margin-bottom: 40px;
        }

        .page_header h1 {
          font-size: 36px;
          color: #3bc8e7;
          margin-bottom: 10px;
        }

        .page_header p {
          color: #777777;
          font-size: 18px;
        }

        .genres_grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 25px;
        }

        .genre_card {
          position: relative;
          border-radius: 15px;
          overflow: hidden;
          height: 200px;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .genre_card:hover {
          transform: translateY(-5px);
          box-shadow: 0 15px 35px rgba(59, 200, 231, 0.3);
        }

        .genre_image {
          position: relative;
          width: 100%;
          height: 100%;
        }

        .genre_image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .genre_overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            135deg,
            rgba(59, 200, 231, 0.8) 0%,
            rgba(27, 32, 57, 0.9) 100%
          );
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          padding: 20px;
          transition: all 0.3s ease;
        }

        .genre_card:hover .genre_overlay {
          background: linear-gradient(
            135deg,
            rgba(59, 200, 231, 0.9) 0%,
            rgba(27, 32, 57, 0.95) 100%
          );
        }

        .genre_content h3 {
          color: #ffffff;
          font-size: 24px;
          margin-bottom: 10px;
          font-weight: 700;
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .genre_content p {
          color: #ffffff;
          font-size: 14px;
          margin-bottom: 15px;
          line-height: 1.5;
          opacity: 0.9;
        }

        .tracks_count {
          color: #ffffff;
          font-size: 12px;
          font-weight: 600;
          background: rgba(255, 255, 255, 0.2);
          padding: 5px 15px;
          border-radius: 20px;
          backdrop-filter: blur(10px);
        }

        @media (max-width: 768px) {
          .genres_grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
          }

          .genre_card {
            height: 180px;
          }

          .genre_content h3 {
            font-size: 20px;
          }

          .genre_content p {
            font-size: 12px;
          }

          .page_header h1 {
            font-size: 28px;
          }
        }

        @media (max-width: 480px) {
          .genres_grid {
            grid-template-columns: 1fr;
          }

          .genre_card {
            height: 160px;
          }
        }
      `}</style>
    </div>
  );
};

export default Genres;
