import React from 'react';

const Albums = () => {
  const albums = [
    { id: 1, title: 'ألبوم الأحلام', artist: 'فنان مشهور', image: '/images/album/album1.jpg', year: '2023' },
    { id: 2, title: 'ألبوم الذكريات', artist: 'فنان آخر', image: '/images/album/album2.jpg', year: '2023' },
    { id: 3, title: 'ألبوم الحب', artist: 'فنان ثالث', image: '/images/album/album3.jpg', year: '2022' },
    { id: 4, title: 'ألبوم الأمل', artist: 'فنان رابع', image: '/images/album/album4.jpg', year: '2022' },
  ];

  return (
    <div className="albums_page">
      <div className="page_header">
        <h1>الألبومات</h1>
        <p>استكشف مجموعة رائعة من الألبومات الموسيقية</p>
      </div>

      <div className="albums_grid">
        {albums.map((album) => (
          <div key={album.id} className="album_card">
            <div className="album_image">
              <img src={album.image} alt={album.title} />
              <div className="album_overlay">
                <button className="play_album_btn">
                  <i className="fa fa-play"></i>
                </button>
              </div>
            </div>
            <div className="album_info">
              <h3>{album.title}</h3>
              <p>{album.artist}</p>
              <span className="album_year">{album.year}</span>
            </div>
          </div>
        ))}
      </div>

      <style>{`
        .albums_page {
          padding: 20px;
          max-width: 1200px;
          margin: 0 auto;
        }

        .page_header {
          text-align: center;
          margin-bottom: 40px;
        }

        .page_header h1 {
          font-size: 36px;
          color: #3bc8e7;
          margin-bottom: 10px;
        }

        .page_header p {
          color: #777777;
          font-size: 18px;
        }

        .albums_grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 30px;
        }

        .album_card {
          background-color: #1b2039;
          border-radius: 15px;
          overflow: hidden;
          transition: all 0.3s ease;
          cursor: pointer;
        }

        .album_card:hover {
          transform: translateY(-10px);
          box-shadow: 0 15px 35px rgba(59, 200, 231, 0.3);
        }

        .album_image {
          position: relative;
          overflow: hidden;
        }

        .album_image img {
          width: 100%;
          height: 250px;
          object-fit: cover;
        }

        .album_overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.7);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        .album_card:hover .album_overlay {
          opacity: 1;
        }

        .play_album_btn {
          width: 70px;
          height: 70px;
          border-radius: 50%;
          background: linear-gradient(45deg, #3bc8e7, #2ba8c7);
          border: none;
          color: #ffffff;
          font-size: 24px;
          cursor: pointer;
          transition: transform 0.3s ease;
        }

        .play_album_btn:hover {
          transform: scale(1.1);
        }

        .album_info {
          padding: 20px;
        }

        .album_info h3 {
          color: #ffffff;
          font-size: 18px;
          margin-bottom: 8px;
          font-weight: 600;
        }

        .album_info p {
          color: #777777;
          font-size: 14px;
          margin-bottom: 8px;
        }

        .album_year {
          color: #3bc8e7;
          font-size: 12px;
          font-weight: 500;
        }

        @media (max-width: 768px) {
          .albums_grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
          }

          .page_header h1 {
            font-size: 28px;
          }
        }
      `}</style>
    </div>
  );
};

export default Albums;
