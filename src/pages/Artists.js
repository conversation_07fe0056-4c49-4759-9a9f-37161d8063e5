import React from 'react';

const Artists = () => {
  const artists = [
    { id: 1, name: 'آفا كورنيش', genre: 'بوب عربي', image: '/images/artist/artist1.jpg', followers: '2.5M' },
    { id: 2, name: 'برايان هيل', genre: 'روك عربي', image: '/images/artist/artist2.jpg', followers: '1.8M' },
    { id: 3, name: 'سارة أحمد', genre: 'كلاسيكي', image: '/images/artist/artist3.jpg', followers: '3.2M' },
    { id: 4, name: 'محمد علي', genre: 'راب عربي', image: '/images/artist/artist4.jpg', followers: '1.1M' },
  ];

  return (
    <div className="artists_page">
      <div className="page_header">
        <h1>الفنانين</h1>
        <p>تعرف على أشهر الفنانين في عالم الموسيقى العربية</p>
      </div>

      <div className="artists_grid">
        {artists.map((artist) => (
          <div key={artist.id} className="artist_card">
            <div className="artist_image">
              <img src={artist.image} alt={artist.name} />
              <div className="artist_overlay">
                <button className="follow_btn">
                  <i className="fa fa-user-plus"></i> متابعة
                </button>
              </div>
            </div>
            <div className="artist_info">
              <h3>{artist.name}</h3>
              <p>{artist.genre}</p>
              <span className="followers">{artist.followers} متابع</span>
            </div>
          </div>
        ))}
      </div>

      <style>{`
        .artists_page {
          padding: 20px;
          max-width: 1200px;
          margin: 0 auto;
        }

        .page_header {
          text-align: center;
          margin-bottom: 40px;
        }

        .page_header h1 {
          font-size: 36px;
          color: #3bc8e7;
          margin-bottom: 10px;
        }

        .page_header p {
          color: #777777;
          font-size: 18px;
        }

        .artists_grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 30px;
        }

        .artist_card {
          background-color: #1b2039;
          border-radius: 15px;
          overflow: hidden;
          transition: all 0.3s ease;
          cursor: pointer;
          text-align: center;
        }

        .artist_card:hover {
          transform: translateY(-10px);
          box-shadow: 0 15px 35px rgba(59, 200, 231, 0.3);
        }

        .artist_image {
          position: relative;
          overflow: hidden;
        }

        .artist_image img {
          width: 100%;
          height: 250px;
          object-fit: cover;
        }

        .artist_overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.7);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        .artist_card:hover .artist_overlay {
          opacity: 1;
        }

        .follow_btn {
          padding: 12px 25px;
          border-radius: 25px;
          background: linear-gradient(45deg, #3bc8e7, #2ba8c7);
          border: none;
          color: #ffffff;
          font-family: 'Cairo', 'Tajawal', sans-serif;
          font-weight: 500;
          cursor: pointer;
          transition: transform 0.3s ease;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .follow_btn:hover {
          transform: scale(1.05);
        }

        .artist_info {
          padding: 20px;
        }

        .artist_info h3 {
          color: #ffffff;
          font-size: 20px;
          margin-bottom: 8px;
          font-weight: 600;
        }

        .artist_info p {
          color: #777777;
          font-size: 14px;
          margin-bottom: 8px;
        }

        .followers {
          color: #3bc8e7;
          font-size: 12px;
          font-weight: 500;
        }

        @media (max-width: 768px) {
          .artists_grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
          }

          .page_header h1 {
            font-size: 28px;
          }
        }
      `}</style>
    </div>
  );
};

export default Artists;
