import React, { useState, useEffect } from 'react';
import RecentMusic from '../components/RecentMusic';
import WeeklyTop from '../components/WeeklyTop';

const Home = () => {
  const [featuredAlbums, setFeaturedAlbums] = useState([]);
  const [recentTracks, setRecentTracks] = useState([]);
  const [topTracks, setTopTracks] = useState([]);

  useEffect(() => {
    // بيانات تجريبية - يمكن استبدالها بـ API calls
    setFeaturedAlbums([
      {
        id: 1,
        title: 'ألبومات هذا الشهر',
        subtitle: 'الأكثر نجاحاً وإبداعاً !',
        description: 'احلم بلحظاتك، حتى التقيت بك، امنحني بعض الشجاعة، الزقاق المظلم، واحد أكثر من غريب، أشياء لا نهائية الأشياء، توقف نبضات القلب، وعود المشي، الألعاب المرغوبة والمزيد...',
        image: '/images/banner.png'
      }
    ]);

    setRecentTracks([
      { id: 1, title: 'احلم بلحظاتك (ثنائي)', artist: 'آفا كورنيش وبرايان هيل', image: '/images/music/r_music1.jpg', duration: '3:45' },
      { id: 2, title: 'حتى التقيت بك', artist: 'آفا كورنيش وبرايان هيل', image: '/images/music/r_music2.jpg', duration: '4:12' },
      { id: 3, title: 'امنحني بعض الشجاعة', artist: 'آفا كورنيش وبرايان هيل', image: '/images/music/r_music3.jpg', duration: '3:28' },
      { id: 4, title: 'الزقاق المظلم الصوتي', artist: 'آفا كورنيش وبرايان هيل', image: '/images/music/r_music4.jpg', duration: '4:05' },
      { id: 5, title: 'وعود المشي', artist: 'آفا كورنيش وبرايان هيل', image: '/images/music/r_music5.jpg', duration: '3:52' }
    ]);

    setTopTracks([
      { id: 1, rank: 1, title: 'حتى التقيت بك', artist: 'آفا كورنيش', image: '/images/weekly/song1.jpg', duration: '5:10' },
      { id: 2, rank: 2, title: 'وعود المشي', artist: 'آفا كورنيش', image: '/images/weekly/song2.jpg', duration: '4:32' },
      { id: 3, rank: 3, title: 'امنحني بعض الشجاعة', artist: 'آفا كورنيش', image: '/images/weekly/song3.jpg', duration: '3:45' },
      { id: 4, rank: 4, title: 'الألعاب المرغوبة', artist: 'آفا كورنيش', image: '/images/weekly/song4.jpg', duration: '4:18' },
      { id: 5, rank: 5, title: 'الزقاق المظلم الصوتي', artist: 'آفا كورنيش', image: '/images/weekly/song5.jpg', duration: '3:55' }
    ]);
  }, []);

  const handlePlay = (track) => {
    console.log('تشغيل الأغنية:', track.title);
    // هنا يمكن إضافة منطق تشغيل الموسيقى
  };

  return (
    <div className="home_wrapper">
      {/* البانر الرئيسي */}
      <div className="banner_section">
        {featuredAlbums.map((album) => (
          <div key={album.id} className="banner_item">
            <div className="banner_content">
              <div className="banner_text">
                <h1>{album.title}</h1>
                <h2>{album.subtitle}</h2>
                <p>{album.description}</p>
                <div className="banner_buttons">
                  <button className="btn_play">
                    <i className="fa fa-play"></i> تشغيل الآن
                  </button>
                  <button className="btn_add">
                    <i className="fa fa-plus"></i> إضافة للمفضلة
                  </button>
                </div>
              </div>
              <div className="banner_image">
                <img src={album.image} alt={album.title} />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* الأغاني المشغلة مؤخراً */}
      <div className="section_wrapper">
        <div className="section_header">
          <h1>تم تشغيلها مؤخراً</h1>
          <a href="#" className="view_all">عرض المزيد</a>
        </div>
        <div className="tracks_grid">
          {recentTracks.map((track) => (
            <div key={track.id} className="track_card">
              <div className="track_image">
                <img src={track.image} alt={track.title} />
                <div className="track_overlay">
                  <button 
                    className="play_btn"
                    onClick={() => handlePlay(track)}
                  >
                    <i className="fa fa-play"></i>
                  </button>
                </div>
              </div>
              <div className="track_info">
                <h3>{track.title}</h3>
                <p>{track.artist}</p>
                <span className="duration">{track.duration}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* أفضل 15 أغنية هذا الأسبوع */}
      <div className="section_wrapper">
        <div className="section_header">
          <h1>أفضل 15 أغنية هذا الأسبوع</h1>
          <a href="#" className="view_all">عرض المزيد</a>
        </div>
        <div className="top_tracks_list">
          {topTracks.map((track) => (
            <div key={track.id} className="top_track_item">
              <div className="track_rank">
                <span>{track.rank}</span>
              </div>
              <div className="track_image_small">
                <img src={track.image} alt={track.title} />
              </div>
              <div className="track_details">
                <h4>{track.title}</h4>
                <p>{track.artist}</p>
              </div>
              <div className="track_duration">
                <span>{track.duration}</span>
              </div>
              <div className="track_actions">
                <button 
                  className="play_btn_small"
                  onClick={() => handlePlay(track)}
                >
                  <i className="fa fa-play"></i>
                </button>
                <button className="add_btn_small">
                  <i className="fa fa-heart-o"></i>
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* الموسيقى الحديثة */}
      <RecentMusic />

      {/* أفضل 15 أغنية هذا الأسبوع */}
      <WeeklyTop />

      <style>{`
        .home_wrapper {
          padding: 20px;
          max-width: 1200px;
          margin: 0 auto;
        }

        /* البانر الرئيسي */
        .banner_section {
          margin-bottom: 50px;
        }

        .banner_item {
          background: linear-gradient(135deg, #1b2039 0%, #14182a 100%);
          border-radius: 20px;
          padding: 40px;
          overflow: hidden;
          position: relative;
        }

        .banner_content {
          display: flex;
          align-items: center;
          gap: 40px;
        }

        .banner_text {
          flex: 1;
        }

        .banner_text h1 {
          font-size: 36px;
          color: #3bc8e7;
          margin-bottom: 10px;
          font-weight: 700;
        }

        .banner_text h2 {
          font-size: 24px;
          color: #ffffff;
          margin-bottom: 20px;
          font-weight: 500;
        }

        .banner_text p {
          color: #777777;
          font-size: 16px;
          line-height: 1.6;
          margin-bottom: 30px;
        }

        .banner_buttons {
          display: flex;
          gap: 15px;
        }

        .btn_play,
        .btn_add {
          padding: 12px 25px;
          border: none;
          border-radius: 25px;
          font-family: 'Cairo', 'Tajawal', sans-serif;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .btn_play {
          background: linear-gradient(45deg, #3bc8e7, #2ba8c7);
          color: #ffffff;
        }

        .btn_add {
          background: transparent;
          border: 2px solid #3bc8e7;
          color: #3bc8e7;
        }

        .btn_play:hover,
        .btn_add:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(59, 200, 231, 0.4);
        }

        .banner_image {
          flex-shrink: 0;
        }

        .banner_image img {
          max-width: 300px;
          height: auto;
          border-radius: 15px;
        }

        /* الأقسام */
        .section_wrapper {
          margin-bottom: 50px;
        }

        .section_header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;
        }

        .section_header h1 {
          font-size: 28px;
          color: #ffffff;
          font-weight: 600;
        }

        .view_all {
          color: #3bc8e7;
          text-decoration: none;
          font-weight: 500;
          transition: color 0.3s ease;
        }

        .view_all:hover {
          color: #ffffff;
        }

        /* شبكة الأغاني */
        .tracks_grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
        }

        .track_card {
          background-color: #1b2039;
          border-radius: 15px;
          overflow: hidden;
          transition: all 0.3s ease;
          cursor: pointer;
        }

        .track_card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 25px rgba(59, 200, 231, 0.3);
        }

        .track_image {
          position: relative;
          overflow: hidden;
        }

        .track_image img {
          width: 100%;
          height: 200px;
          object-fit: cover;
        }

        .track_overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.7);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        .track_card:hover .track_overlay {
          opacity: 1;
        }

        .play_btn {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          background: linear-gradient(45deg, #3bc8e7, #2ba8c7);
          border: none;
          color: #ffffff;
          font-size: 20px;
          cursor: pointer;
          transition: transform 0.3s ease;
        }

        .play_btn:hover {
          transform: scale(1.1);
        }

        .track_info {
          padding: 15px;
        }

        .track_info h3 {
          color: #ffffff;
          font-size: 16px;
          margin-bottom: 5px;
          font-weight: 600;
        }

        .track_info p {
          color: #777777;
          font-size: 14px;
          margin-bottom: 8px;
        }

        .duration {
          color: #3bc8e7;
          font-size: 12px;
          font-weight: 500;
        }

        /* قائمة أفضل الأغاني */
        .top_tracks_list {
          background-color: #1b2039;
          border-radius: 15px;
          overflow: hidden;
        }

        .top_track_item {
          display: flex;
          align-items: center;
          padding: 15px 20px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          transition: background-color 0.3s ease;
        }

        .top_track_item:hover {
          background-color: rgba(59, 200, 231, 0.1);
        }

        .top_track_item:last-child {
          border-bottom: none;
        }

        .track_rank {
          width: 40px;
          text-align: center;
          margin-left: 15px;
        }

        .track_rank span {
          color: #3bc8e7;
          font-size: 18px;
          font-weight: 700;
        }

        .track_image_small {
          width: 50px;
          height: 50px;
          border-radius: 8px;
          overflow: hidden;
          margin-left: 15px;
        }

        .track_image_small img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .track_details {
          flex: 1;
          margin-left: 15px;
        }

        .track_details h4 {
          color: #ffffff;
          font-size: 16px;
          margin-bottom: 3px;
          font-weight: 500;
        }

        .track_details p {
          color: #777777;
          font-size: 14px;
        }

        .track_duration {
          margin-left: 20px;
        }

        .track_duration span {
          color: #777777;
          font-size: 14px;
        }

        .track_actions {
          display: flex;
          gap: 10px;
        }

        .play_btn_small,
        .add_btn_small {
          width: 35px;
          height: 35px;
          border-radius: 50%;
          border: none;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .play_btn_small {
          background: linear-gradient(45deg, #3bc8e7, #2ba8c7);
          color: #ffffff;
        }

        .add_btn_small {
          background: transparent;
          border: 1px solid #777777;
          color: #777777;
        }

        .play_btn_small:hover,
        .add_btn_small:hover {
          transform: scale(1.1);
        }

        .add_btn_small:hover {
          border-color: #3bc8e7;
          color: #3bc8e7;
        }

        /* التصميم المتجاوب */
        @media (max-width: 768px) {
          .home_wrapper {
            padding: 15px;
          }

          .banner_content {
            flex-direction: column;
            text-align: center;
            gap: 20px;
          }

          .banner_text h1 {
            font-size: 28px;
          }

          .banner_text h2 {
            font-size: 20px;
          }

          .banner_buttons {
            justify-content: center;
          }

          .tracks_grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
          }

          .section_header {
            flex-direction: column;
            gap: 10px;
            text-align: center;
          }

          .top_track_item {
            padding: 10px 15px;
          }

          .track_details h4 {
            font-size: 14px;
          }

          .track_details p {
            font-size: 12px;
          }
        }
      `}</style>
    </div>
  );
};

export default Home;
