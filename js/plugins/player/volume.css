html {
  font-size: 1px;
}

body {
  font-size: 14rem;
}

* {
  box-sizing: border-box;
}

.knob-container.nolabel .knob-wrapper-outer {
  top: 0;
}

.knob-container .knob-wrapper-outer input {
  bottom: 0;
  position: absolute;
  z-index: 1;
}
.knob-container .knob-wrapper-outer.nogauge .knob:after, .knob-container .knob-wrapper-outer.nogauge .knob-mask:after {
  display: none;
}
.knob-container .knob-wrapper-outer.nogauge .knob-wrapper .knob:before {
  content: "";
  position: absolute;
  top: auto;
  bottom: 6rem;
  left: 6rem;
  height: 7rem;
  width: 7rem;
  background: #fff;
  border: none !important;
  border-radius: 100% !important;
  box-shadow: 0 0 0 3rem rgba(0, 0, 255, 0.2), 0 0 2px 0 #fafafa;
}
.knob-container .knob-wrapper-outer .knob-wrapper {
  width: 55px;
  position: relative;
  cursor: -webkit-grab;
}
.knob-container .knob-wrapper-outer .knob-wrapper:active {
  cursor: -webkit-grabbing;
}
.knob-container .knob-wrapper-outer .knob-wrapper .knob-mask {
  width: 100%;
  padding-bottom: 100%;
  border-radius: 100%;
  pointer-events: none;
  position: relative;
}
.knob-container .knob-wrapper-outer .knob-wrapper .knob-mask:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 100%;
  border: 5px solid grey;
  z-index: 0;
}
.knob-container .knob-wrapper-outer .knob-wrapper .knob-mask:after {
  content: "";
  position: absolute;
  top: 0rem;
  left: 0rem;
  right: 0rem;
  bottom: 0rem;
  border-radius: 100%;
  border: 5px solid transparent;
	border-bottom-color: grey;
}
.knob-container .knob-wrapper-outer .knob-wrapper .round {
    content: "";
    position: absolute;
    top: 14rem;
    left: 18rem;
    right: 10rem;
    bottom: 18rem;
    border-radius: 100%;
}
.knob-container .knob-wrapper-outer .knob-wrapper .knob {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  transform: rotateZ(270deg);
}
.knob-container .knob-wrapper-outer .knob-wrapper .knob:after {
  content: "";
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  border-radius: 100%;
  border: 3rem solid transparent;
  border-bottom-color: #fff;
}
.knob-container .knob-wrapper-outer .knob-wrapper .knob.d2:after {
  border-right-color: #fff;
}
.knob-container .knob-wrapper-outer .knob-wrapper .knob.d3:after {
  border-top-color: #fff;
  border-right-color: #fff;
}
.knob-container .knob-wrapper-outer .knob-wrapper .handle {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
  transform: rotateZ(270deg);
}
.knob-container .knob-wrapper-outer .knob-wrapper .handle:before {
  content: "";
  position: absolute;
  bottom: 6rem;
  left: 2rem;
  height: 14px;
  width: 14px;
  background: #fff;
  border: none !important;
  border-radius: 100% !important;
}
.knob-container .knob-wrapper-outer .knob-wrapper .handle:after {
  content: "";
  position: absolute;
  bottom: 0rem;
  left: -1rem;
  height: 20rem;
  width: 20rem;
  border: none !important;
  border-radius: 100% !important;
}
.knob-container .knob-wrapper-outer .knob-wrapper .pip {
  position: absolute;
  font-size: 11rem;
  color: black;
  pointer-events: none;
}
.knob-container .knob-wrapper-outer .knob-wrapper .pip.min {
  bottom: 6rem;
  left: 0;
  right: 50%;
  width: 50%;
  text-align: center;
}
.knob-container .knob-wrapper-outer .knob-wrapper .pip.max {
  bottom: 6rem;
  left: 50%;
  width: 50%;
  right: 0;
  text-align: center;
}
